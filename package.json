{"name": "@mfe/waimai-cd-fe-bee-assistant", "version": "1.0.0", "description": "MRN模版", "author": "jiangliancheng", "scripts": {"build": "mrn build", "localBuild": "mrn build -a waimai_mfe_bee --entry<PERSON><PERSON> bee-assistant-main", "lint": "eslint --ext .ts,.tsx,.js,.jsx ./", "fix": "yarn lint --fix", "start": "mrn start", "startMain": "mrn start --entry<PERSON><PERSON> bee-assistant-main --port 4783 | grep -E 'BEE_ASSISTANT_DEBUG|BEE_ASSISTANT_DEBUG_END'", "startMain2": "mrn start --entry<PERSON><PERSON> bee-assistant-main --port 4782", "startMain3": "cross-env NODE_OPTIONS=\"--openssl-legacy-provider --max_old_space_size=4096\" mrn start --entryName bee-assistant-main --port 8081 --maxWorkers 1", "dev": "mrn start --port 8478 --entry<PERSON><PERSON> waimai-mfe-bee", "autolink": "node autolink.js", "sync-dts": "sync-dts", "parse-yapi": "parse-yapi"}, "dependencies": {"@analytics/mrn-sdk": "^1.8.1", "@ant-design/colors": "^7.1.0", "@mfe/bee-foundation-moses": "0.1.4-beta.2", "@mfe/bee-foundation-navigation": "1.2.1", "@mfe/bee-foundation-utils": "^6.0.6", "@mfe/cc-api-caller-bee": "^0.2.15", "@mfe/crm-ai-bee": "^0.0.27", "@mfe/waimai-mfe-bee-common": "^2.0.77", "@mrn/mrn-webview": "^1.0.4", "@mrn/react-native-linear-gradient": "^1.1.7", "@utiljs/param": "^0.6.11", "@wuba/react-native-echarts": "^2.0.3", "ahooks": "^3.7.8", "dayjs": "^1.11.13", "echarts": "^6.0.0", "immer": "^10.0.3", "lodash": "^4.17.21", "react-native-device-info": "^0.21.5", "react-native-draggable-flatlist": "1", "react-native-image-zoom-viewer": "^3.0.1", "react-native-keyboard-manager": "^6.5.16-0", "react-native-markdown-display": "7", "react-native-modal": "^14.0.0-rc.1", "react-native-orientation": "^3.1.3", "react-native-permissions": "^3.2.0", "react-native-walkthrough-tooltip": "^1.6.0", "react-native-webview": "^10.9.0", "swr": "^2.3.3", "zustand": "^4.4.6"}, "devDependencies": {"@mfe/react-native-video": "^2.2.2", "@mfe/url-parse": "^1.5.10", "@mrn/mrn-base": "3.0.47", "@mrn/mrn-cli": "git+ssh://*******************/~jiangliancheng/mrn-cli.git#v3.0.3-bee1.1", "@mrn/mrn-knb": "^0.5.0", "@mrn/mrn-utils": "^1.5.0", "@mrn/react-native": "3.0.26", "@react-native-community/eslint-config": "^3.2.0", "@roo/roo-rn": "^1.0.6", "@testing-library/react-hooks": "^8.0.1", "@testing-library/react-native": "12", "@types/lodash": "^4.17.0", "@types/react": "^17.0.43", "cross-env": "^10.0.0", "eslint-plugin-ft-flow": "^3.0.10", "eslint-plugin-import": "^2.29.1", "eslint-plugin-unused-imports": "3", "lint-staged": "12.0.0", "metro-react-native-babel-preset": "0.59.0", "typescript": "^5.8.3"}, "resolutions": {"@types/react": "^17.0.43", "@roo/roo-rn": "^1.0.6", "react-native-svg": "12.1.0", "@mrn/react-native": "3.0.26", "@types/node": "16.9.1", "@mrn/mrn-base": "3.0.47", "@mfe/waimai-mfe-bee-common": "2.0.77", "@mfe/bee-foundation-navigation": "1.2.1", "@mfe/bee-foundation-utils": "6.0.6"}, "eslintIgnore": ["dist"], "files": ["dist", "src", "index.tsx", "mrn.config.js", "readme.md"], "husky": {"hooks": {"pre-commit": "yarn lint-staged", "post-merge": "yarn"}}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint"]}, "main": "index.tsx", "volta": {"node": "16.20.2", "yarn": "1.22.22"}}