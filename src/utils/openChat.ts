import { openPage } from '@mfe/bee-foundation-utils';

import { SOURCE } from '../types';

const openChat = (source: SOURCE, restProps = {}) => {
    const data = encodeURIComponent(
        JSON.stringify({ source: source, ...restProps }),
    );

    openPage(
        `meituanwaimaibee://beewaimai.meituan.com/mrn?mrn_biz=waimaicrm&mrn_entry=bee-assistant-main&mrn_component=bee-assistant&initialRoute=Chat&data=${data}&new_bundle=1`,
    );
};
export default openChat;
