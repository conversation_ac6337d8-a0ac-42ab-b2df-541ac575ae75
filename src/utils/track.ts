import LX from '@analytics/mrn-sdk';

export const enum TrackEventType {
    MC = 'mc',
    MV = 'mv',
}

const chatCid = 'c_waimai_e_bee_rn_assistant_chat';
const category = 'waimai_e';
export const pageInfoKey = LX.getPageInfoKey(chatCid);

const assistantCidPrefix = 'c_waimai_e_bee_rn_assistant';
export const track = (view: string, extra = {}) => {
    if (!view) {
        return;
    }

    // 页面埋点, bee_common中已经添加了bee_rn前缀，直接使用ocean中的配置View即可
    // assistant_是小蜜助手子包特有前缀
    LX.pageView({
        pageInfoKey,
        cid: `${assistantCidPrefix}_${view}`,
        category,
        ...extra,
    });
};

export const trackEvent = (
    event: string,
    obj = {},
    type = TrackEventType.MC,
    extra = {},
) => {
    if (!event) {
        return;
    }

    const eventName = `b_waimai_e_bee_rn_assistant_${event}_${type}`;
    if (type === TrackEventType.MC) {
        LX.moduleClick({
            cid: chatCid,
            bid: eventName,
            valLab: obj,
            pageInfoKey,
            ...extra,
        });
        return;
    }
    LX.moduleView({
        cid: chatCid,
        bid: eventName,
        valLab: obj,
        pageInfoKey,
        ...extra,
    });
};

export const trackButtonClick = (click_name: string) => {
    if (!click_name) {
        return;
    }
    trackEvent('chat_button_click', { click_name }, TrackEventType.MC);
};
