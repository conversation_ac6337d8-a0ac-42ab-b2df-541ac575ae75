import splitAnswer from './splitAnswer';
import { Message } from '../types/message';

// 支持的非文本类型的消息
// const TYPES_SUPPORT = [
//     'video',
//     'image',
//     'media',
//     'options',
//     'separator',
//     'buttons',
//     'suffixOptions',
//     'markdown',
//     'selector',
//     'selectorItem',
//     'cardWithAvatar',
//     'config',
// ];

const BLOCK_TYPES = [
    'media',
    'options',
    'buttons',
    'selector',
    'selectorItem',
    'cardWithAvatar',
];

// 块级元素上下的文本去除和块级元素相邻的空白字符
const trimMessage = (res: Message[]) => {
    return (
        res
            .map((d, i) => {
                const v = { ...d };
                if (v.type === 'text') {
                    if (BLOCK_TYPES.includes(res[i + 1]?.type)) {
                        v.insert = v.insert.trimEnd();
                    }
                    if (BLOCK_TYPES.includes(res[i - 1]?.type)) {
                        v.insert = v.insert.trimStart();
                    }
                }
                return v;
            })
            // 经过上一步后可能会有空白字符，这一步剔除这些空白字符
            .filter((v) => Boolean(v.insert))
    );
};

const castMediaItem = (deltaWithType: Message[]) => {
    const res = [];
    // 图片和视频同行展示
    for (let i = 0; i < deltaWithType.length; i++) {
        const cur = deltaWithType[i];
        if (!['image', 'video'].includes(cur.type)) {
            res.push(cur);
            continue;
        }

        const lastEle = res[res.length - 1] || { type: '' };
        if (lastEle.type === 'media') {
            lastEle.insert.media.push(cur.insert);
            continue;
        }
        res.push({
            type: 'media',
            insert: { media: [cur.insert] },
        });
    }
    return res;
};

const pic = {
    test: (str: string) => /!\[.*?]\((.*?)\)/.test(str),
    text2Obj: (str: string) => {
        const value = str.split(/!\[.*?]\((.*?)\)/).filter((v) => v);
        return { url: value[0] };
    },
};
// const link = {
//     test: (str: string) => /\[.*?]\((.*?)\)/.test(str),
//     text2Obj: (str: string) => {
//         const value = str.split(/\[(.*?)]\((.*?)\)/).filter((v) => v);
//         return { text: value[0], url: value[1] };
//     },
// };

// 从md文本解析图片数据
export const splitMediaFromMarkdown = (md: string) => {
    const splitPattern = /(!\[.*?]\(.*?\))/; // md图片

    const castMarkdown = (children: string) => {
        let res = [];
        if (pic.test(children)) {
            const parts = children.split(splitPattern);
            res = parts
                .filter((v) => v && !/^\s*$/.test(v))
                .filter(Boolean)
                .map((p) => {
                    if (pic.test(p)) {
                        const { url } = pic.text2Obj(p);
                        return {
                            type: 'image',
                            insert: { image: url },
                        };
                    }
                    return {
                        type: 'text',
                        insert: p,
                    };
                });
        } else {
            res = [{ type: 'text', insert: children }];
        }
        return res;
    };
    return castMediaItem(castMarkdown(md));
};

const delta2message = (str: string | any[]) => {
    if (!str) {
        return [];
    }
    // 如果str可以解析为json，则走新逻辑，否则走老逻辑
    try {
        const delta: any[] = typeof str === 'string' ? JSON.parse(str) : str;
        if (!Array.isArray(delta)) {
            throw new Error('数字文本');
        }
        if (!delta.length) {
            return delta;
        }

        // 文本类型消息处理，区分普通文本、链接文本和带样式的文本
        const deltaWithType: Message[] = delta.map((v) => {
            if (typeof v.insert !== 'string' || v.type === 'hideSpan') {
                return v;
            }

            // 链接消息
            if (v.attributes?.link) {
                return { ...v, type: 'link' };
            }
            // 带样式的文本消息
            if (v.attributes?.bold || v.attributes?.color) {
                return { ...v, type: 'styledText' };
            }
            return { ...v, type: 'text' };
        });

        const finalRes = [];
        deltaWithType.forEach((v) => {
            finalRes.push(v);
        });

        const res: Message[] = castMediaItem(finalRes);

        return trimMessage(res);
    } catch (e) {
        // 主要是JSON解析可能出错
        try {
            return trimMessage(splitAnswer(str as string));
        } catch (v) {
            return [{ type: 'unknown' }];
        }
    }
};
export default delta2message;
