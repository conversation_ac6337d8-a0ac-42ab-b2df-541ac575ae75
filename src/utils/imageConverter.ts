import { apiCaller } from '@mfe/cc-api-caller-bee';

import NetImages from '@/assets/images/homeRefactor';

/**
 * 检查图片URL是否需要转换
 * @param imageUrl 图片链接
 * @returns 是否需要转换
 */
export const shouldConvertImage = (imageUrl: string): boolean => {
    return (
        imageUrl.startsWith('https://km.sankuai.com') ||
        imageUrl.startsWith('https://km.it.st.sankuai.com') ||
        imageUrl.startsWith('https://km.it.test.sankuai.com')
    );
};

/**
 * 调用接口转换Wiki图片链接为S3链接
 * @param wikiPictureUrl 原始Wiki图片链接
 * @returns 转换后的S3链接，转换失败时返回null
 */
export const convertWikiPicToS3Url = async (
    wikiPictureUrl: string,
): Promise<string | null> => {
    try {
        const res = await apiCaller.get(
            '/bee/v2/bdaiassistant/common/convertWikiPicToS3Url',
            {
                wikiPictureUrl,
            },
        );

        if (res.code !== 0) {
            console.warn('图片链接转换失败:', res.msg || '未知错误');
            return null;
        }

        return res.data?.s3Url || null;
    } catch (error) {
        console.error('图片链接转换接口调用失败:', error);
        return null;
    }
};

/**
 * 图片转换缓存，避免重复转换同一张图片
 */
const conversionCache = new Map<string, string | null>();

/**
 * 全局图片转换映射表，key为原始src，value为转换后的新链接
 */
const globalConversionMap = new Map<string, string>();

/**
 * 获取图片的最终显示URL（如果有转换则返回转换后的，否则返回原始URL）
 */
export const getFinalImageUrl = (originalUrl: string): string => {
    return globalConversionMap.get(originalUrl) || originalUrl;
};

/**
 * 带缓存的图片链接转换函数
 * @param wikiPictureUrl 原始Wiki图片链接
 * @returns 转换后的S3链接，转换失败时返回null
 */
export const convertWikiPicToS3UrlWithCache = async (
    wikiPictureUrl: string,
): Promise<string | null> => {
    // 检查缓存
    if (conversionCache.has(wikiPictureUrl)) {
        return conversionCache.get(wikiPictureUrl);
    }

    // 执行转换
    const s3Url = await convertWikiPicToS3Url(wikiPictureUrl);

    // 缓存结果
    conversionCache.set(wikiPictureUrl, s3Url);

    // 如果转换成功，更新全局转换映射表
    globalConversionMap.set(wikiPictureUrl, s3Url || NetImages.defaultImage);

    return s3Url;
};

/**
 * 清理转换缓存（可选，用于内存管理）
 */
export const clearConversionCache = (): void => {
    conversionCache.clear();
    globalConversionMap.clear();
};

/**
 * 验证图片URL是否有效
 * @param imageUrl 图片链接
 * @returns 是否为有效的图片URL
 */
export const isValidImageUrl = (imageUrl: string): boolean => {
    if (!imageUrl || typeof imageUrl !== 'string') {
        return false;
    }

    try {
        const url = new URL(imageUrl);
        return url.protocol === 'http:' || url.protocol === 'https:';
    } catch {
        return false;
    }
};
