import { useEffect } from 'react';
import { DeviceEventEmitter } from 'react-native';

import { trackEvent } from './track';
import {
    mockSignInNotification,
    mockSignOutNotification,
} from '../api/mockData';
import { useVisit } from '../hooks/useVisit';
import { SOURCE } from '../types';

import { PoiItem } from '@/components/Visit/PoiBubble';
import openChat from '@/utils/openChat';

// 消息类型
interface PopupNoticeMessage {
    tag: 'popupNotice';
    content: {
        noticeType: 'visitCheckIn' | 'visitCheckOut';
        poiList: Array<PoiItem>;
    };
}

// 事件名称常量
export const EVENTS = {
    SHOW_POI_SIGN_IN_BUBBLE: 'SHOW_POI_SIGN_IN_BUBBLE',
    SHOW_POI_SIGN_OUT_BUBBLE: 'SHOW_POI_SIGN_OUT_BUBBLE',
};

/**
 * 处理签入提醒消息
 * @param message 消息内容
 * @param visit 拜访Hook
 */
const handleSignInNotice = (
    message: PopupNoticeMessage,
    visit: ReturnType<typeof useVisit>,
) => {
    const { poiList } = message.content;

    if (!poiList || poiList.length === 0) {
        return;
    }

    // 转换数据格式
    const formattedPoiList = poiList.map((poi) => ({
        poiId: poi.poiId,
        name: poi.name,
        avatar: poi.avatar,
        distance: poi.distance || '未知',
        isCheckIn: poi.isCheckIn || false,
        checkInTime: poi.checkInTime,
    }));

    // 定义回调函数
    const handlePoiSignIn = (poiId) => {
        visit.handlePoiSignIn(poiId);

        openChat(SOURCE.home, {
            triggerQuery: '查看更多可签入商家',
            aichat_scene_id: 'visit_check_in_list',
        });
    };

    const handleRefreshLocation = () => {
        visit.refreshUserLocation();
    };

    const handleMorePress = async () => {
        trackEvent('visit_view_more_pois');

        try {
            // 1. 先刷新用户当前定位
            await visit.refreshUserLocation();

            // 2. 使用openChat方法打开小蜜会话页面，并传递参数触发查询
            openChat(SOURCE.home, {
                triggerQuery: '查看更多可签入商家',
                aichat_scene_id: 'visit_check_in_list',
            });
        } catch (error) {
            console.error('刷新位置或跳转失败:', error);
        }
    };

    // 使用事件发布订阅模式发送显示签入商家气泡的事件
    DeviceEventEmitter.emit(EVENTS.SHOW_POI_SIGN_IN_BUBBLE, {
        poiList: formattedPoiList,
        callbacks: {
            onPoiSignIn: handlePoiSignIn,
            onRefreshLocation: handleRefreshLocation,
            onMorePress: handleMorePress,
        },
    });
};

/**
 * 处理签出提醒消息
 * @param message 消息内容
 * @param visit 拜访Hook
 */
const handleSignOutNotice = (
    message: PopupNoticeMessage,
    visit: ReturnType<typeof useVisit>,
) => {
    const { poiList } = message.content;

    if (!poiList || poiList.length === 0) {
        return;
    }

    const poi = poiList[0];

    if (!poi.url) {
        console.error('签出提醒缺少跳转链接');
        return;
    }

    // 定义回调函数
    const handleSignOut = (url) => {
        visit.handlePoiSignOut(url);
    };

    // 使用事件发布订阅模式发送显示签出商家气泡的事件
    DeviceEventEmitter.emit(EVENTS.SHOW_POI_SIGN_OUT_BUBBLE, {
        poiInfo: {
            poiId: poi.poiId,
            name: poi.name,
            avatar: poi.avatar,
            url: poi.url,
            checkInTime: poi.checkInTime || Date.now() - 3600000, // 如果没有签入时间，默认为1小时前
        },
        callbacks: {
            onSignOut: handleSignOut,
        },
    });
};

/**
 * 消息监听Hook
 * @param visit 拜访Hook
 */
export const useMessageListener = (visit: ReturnType<typeof useVisit>) => {
    useEffect(() => {
        // 模拟消息推送
        const simulateMessages = () => {
            // 随机决定是否推送消息
            const shouldPushMessage = Math.random() > 0;

            if (shouldPushMessage) {
                // 随机决定推送签入还是签出消息
                const isSignIn = Math.random() > 0;

                if (isSignIn) {
                    console.log('模拟签入提醒消息');
                    const message = mockSignInNotification();
                    handleSignInNotice(message, visit);
                } else {
                    console.log('模拟签出提醒消息');
                    const message = mockSignOutNotification();
                    handleSignOutNotice(message, visit);
                }
            }

            // 继续模拟
            setTimeout(simulateMessages, 300000); // 10秒模拟一次
        };

        // 开始模拟
        const timerId = setTimeout(simulateMessages, 5000); // 5秒后开始模拟

        // 组件卸载时停止模拟
        return () => {
            clearTimeout(timerId);
        };
    }, [visit]);
};

/**
 * 处理WebSocket消息
 * @param message 消息内容
 * @param visit 拜访Hook
 */
export const handleWebSocketMessage = (
    message: any,
    visit: ReturnType<typeof useVisit>,
) => {
    try {
        const data = JSON.parse(message);

        if (data.tag === 'popupNotice') {
            if (data.content.noticeType === 'visitCheckIn') {
                handleSignInNotice(data, visit);
            } else if (data.content.noticeType === 'visitCheckOut') {
                handleSignOutNotice(data, visit);
            }
        }
    } catch (error) {
        console.error('处理WebSocket消息失败:', error);
    }
};

/**
 * 手动触发签入提醒（用于调试）
 * @param visit 拜访Hook
 */
export const triggerSignInReminder = (visit: ReturnType<typeof useVisit>) => {
    const message = mockSignInNotification();
    handleSignInNotice(message, visit);
};

/**
 * 手动触发签出提醒（用于调试）
 * @param visit 拜访Hook
 */
export const triggerSignOutReminder = (visit: ReturnType<typeof useVisit>) => {
    const message = mockSignOutNotification();
    handleSignOutNotice(message, visit);
};
