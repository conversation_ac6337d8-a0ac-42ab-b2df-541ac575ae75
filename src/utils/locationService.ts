import { mockLocationInfo } from '../api/mockData';

// 位置信息类型
export interface LocationInfo {
    latitude: number;
    longitude: number;
    timestamp: number;
}

// 位置信息缓存，最多保存10条
const locationCache: LocationInfo[] = [];
const MAX_CACHE_SIZE = 10;

// 定时器ID
let locationTimerId: NodeJS.Timeout | null = null;
// 默认位置获取间隔（毫秒）
const DEFAULT_INTERVAL = 60000; // 1分钟

/**
 * 获取当前位置
 * @returns Promise<LocationInfo>
 */
export const getCurrentLocation = async (): Promise<LocationInfo> => {
    // 使用mock数据
    const location = mockLocationInfo();
    console.log('获取位置成功(mock):', location);
    return location;
};

/**
 * 添加位置信息到缓存
 * @param location 位置信息
 */
export const addLocationToCache = (location: LocationInfo): void => {
    locationCache.unshift(location);

    // 保持缓存大小不超过最大值
    if (locationCache.length > MAX_CACHE_SIZE) {
        locationCache.pop();
    }
};

/**
 * 上报位置信息
 */
export const reportLocation = async (): Promise<void> => {
    try {
        if (locationCache.length === 0) {
            return;
        }

        // 复制当前缓存中的位置信息
        const locationsToReport = [...locationCache];

        // 模拟上报成功
        console.log('位置信息上报成功(mock):', locationsToReport);
    } catch (error) {
        console.error('位置信息上报失败:', error);
    }
};

/**
 * 开始定期获取和上报位置
 * @param interval 获取位置的间隔时间（毫秒）
 */
export const startLocationTracking = (
    interval: number = DEFAULT_INTERVAL,
): void => {
    // 如果已经在跟踪位置，先停止
    if (locationTimerId) {
        stopLocationTracking();
    }

    // 立即获取一次位置
    getCurrentLocation()
        .then((location) => {
            addLocationToCache(location);
            reportLocation();
        })
        .catch((error) => {
            console.error('初始位置获取失败:', error);
        });

    // 设置定时器，定期获取位置
    locationTimerId = setInterval(async () => {
        try {
            const location = await getCurrentLocation();
            addLocationToCache(location);

            // 每获取3次位置后上报一次
            if (locationCache.length % 3 === 0) {
                reportLocation();
            }
        } catch (error) {
            console.error('定期获取位置失败:', error);
        }
    }, interval);
};

/**
 * 停止位置跟踪
 */
export const stopLocationTracking = (): void => {
    if (locationTimerId) {
        clearInterval(locationTimerId);
        locationTimerId = null;

        // 停止跟踪时上报一次位置
        reportLocation();
    }
};

/**
 * 获取缓存中的位置信息
 * @returns LocationInfo[]
 */
export const getCachedLocations = (): LocationInfo[] => {
    return [...locationCache];
};

/**
 * 刷新位置信息
 * @returns Promise<LocationInfo>
 */
export const refreshLocation = async (): Promise<LocationInfo> => {
    try {
        const location = await getCurrentLocation();
        addLocationToCache(location);
        return location;
    } catch (error) {
        console.error('刷新位置失败:', error);
        throw error;
    }
};
