import { SafeAreaProvider } from '@mfe/bee-foundation-navigation';
import { deviceEventEmitter } from '@mrn/mrn-utils';
// @ts-ignore
import { Platform, unstable_RootTagContext } from '@mrn/react-native';
import { MTDProvider } from '@roo/roo-rn';
import React, { useContext, useEffect } from 'react';
import KeyboardManager from 'react-native-keyboard-manager';

import { VisitProvider } from './contexts/VisitContext';
import { id } from './hooks/useMessage';
import { StackRouter } from './routers';
import { ModalWrapper } from './store/slideModal';

export const App = (props) => {
    const rootTag = useContext(unstable_RootTagContext) as string;
    useEffect(() => {
        if (Platform.OS !== 'ios') {
            return;
        }
        KeyboardManager.setEnable(false);
        const sub1 = deviceEventEmitter.addListener(
            'containerViewDidAppear',
            () => KeyboardManager.setEnable(false),
        );
        return () => {
            sub1.remove();
        };
    }, []);
    return (
        <SafeAreaProvider key={rootTag || id}>
            <MTDProvider>
                <VisitProvider>
                    {/* <StackRouter {...props} initialRoute="DraggedFlatListDemo" /> */}
                    <StackRouter {...props} />
                    <ModalWrapper />
                </VisitProvider>
            </MTDProvider>
        </SafeAreaProvider>
    );
};
