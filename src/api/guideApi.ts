// Mock 数据，模拟服务器存储的引导状态
let mockGuideStatus: Record<string, boolean> = {
    visit_checkin_guide: false,
};

/**
 * 获取引导状态
 * @param guideKey 引导唯一标识
 * @returns 引导是否已完成
 */
export const getGuideStatus = async (guideKey: string): Promise<boolean> => {
    // 实际项目中应该调用后端接口
    // return apiCaller.get('/bee/v1/bdaiassistant/guide/status', { guideKey });

    // Mock 实现
    console.log(
        `[Mock API] 获取引导状态: ${guideKey} = ${mockGuideStatus[guideKey]}`,
    );
    return Promise.resolve(mockGuideStatus[guideKey] || false);
};

/**
 * 设置引导状态
 * @param guideKey 引导唯一标识
 * @param completed 是否已完成
 */
export const setGuideStatus = async (
    guideKey: string,
    completed: boolean,
): Promise<void> => {
    // 实际项目中应该调用后端接口
    // return apiCaller.post('/bee/v1/bdaiassistant/guide/status', { guideKey, completed });

    // Mock 实现
    mockGuideStatus[guideKey] = completed;
    console.log(`[Mock API] 设置引导状态: ${guideKey} = ${completed}`);
    return Promise.resolve();
};

/**
 * 重置引导状态（用于测试）
 * @param guideKey 引导唯一标识
 */
export const resetGuideStatus = async (guideKey: string): Promise<void> => {
    // 实际项目中应该调用后端接口
    // return apiCaller.post('/bee/v1/bdaiassistant/guide/reset', { guideKey });

    // Mock 实现
    mockGuideStatus[guideKey] = false;
    console.log(`[Mock API] 重置引导状态: ${guideKey} = false`);
    return Promise.resolve();
};
