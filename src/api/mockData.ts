/**
 * 拜访签入签出提醒相关的mock数据
 */

// 模拟商家头像
const poiAvatars = [
    'https://p0.meituan.net/merchantpic/2f258bdda96a0d0da2eaf57aeadb933568845.jpg',
    'https://p0.meituan.net/merchantpic/a53463221d3f5ef2a3c14b9f3c66f86f46182.jpg',
    'https://p0.meituan.net/merchantpic/02be403e97d4a9a11aa71d1e7e3f4b9e51722.jpg',
    'https://p0.meituan.net/merchantpic/c80a0e326f1c32e3ae5e4436f6b5288a38140.jpg',
    'https://p0.meituan.net/merchantpic/d7a3f55d5d28fbe95f9a5dfe12a5493c54134.jpg',
];

// 模拟商家名称
const poiNames = [
    '老王烧烤店',
    '小李炒饭',
    '张三麻辣烫',
    '李四奶茶店',
    '王五便当',
    '赵六水饺',
    '钱七炸鸡',
    '孙八火锅',
    '周九烤肉',
    '吴十面馆',
];

/**
 * 生成随机整数
 * @param min 最小值
 * @param max 最大值
 * @returns 随机整数
 */
const getRandomInt = (min: number, max: number): number => {
    return Math.floor(Math.random() * (max - min + 1)) + min;
};

/**
 * 生成随机商家信息
 * @param poiId 商家ID
 * @param isCheckIn 是否已签入
 * @returns 商家信息
 */
const generatePoiInfo = (poiId: number, isCheckIn: boolean = false) => {
    const distance = getRandomInt(50, 500);
    const checkInTime = isCheckIn
        ? Date.now() - getRandomInt(5, 30) * 60 * 1000
        : undefined;

    return {
        poiId,
        name: poiNames[getRandomInt(0, poiNames.length - 1)],
        avatar: poiAvatars[getRandomInt(0, poiAvatars.length - 1)],
        distance: `${distance}m`,
        isCheckIn,
        checkInTime,
    };
};

/**
 * 模拟推荐签入商家列表
 * @param count 商家数量
 * @returns 商家列表
 */
export const mockRecommendCheckInPois = (count: number = 3) => {
    const pois = [];

    // 生成未签入的商家
    for (let i = 1; i <= count; i++) {
        pois.push(generatePoiInfo(1000 + i));
    }

    // 添加一个已签入的商家
    if (count > 3) {
        pois.push(generatePoiInfo(2000, true));
    }

    return pois;
};

/**
 * 模拟推荐签出商家信息
 * @returns 签出商家信息
 */
export const mockRecommendCheckOutPoi = () => {
    const poiId = 2000;
    return {
        poiId,
        name: poiNames[getRandomInt(0, poiNames.length - 1)],
        avatar: poiAvatars[getRandomInt(0, poiAvatars.length - 1)],
        url: `https://example.com/checkout?poiId=${poiId}`,
        checkInTime: Date.now() - 30 * 1000,
    };
};

/**
 * 模拟用户签入提醒配置
 * @returns 用户配置
 */
export const mockUserReminderConfig = () => {
    return {
        switch: true,
        period: 'daily',
        startTime: '08:00',
        endTime: '18:00',
    };
};

/**
 * 模拟签入成功响应
 * @param poiId 商家ID
 * @returns 签入响应
 */
export const mockSignInResponse = (poiId: number) => {
    const poiName = poiNames[getRandomInt(0, poiNames.length - 1)];
    return {
        success: true,
        poiId,
        poiName,
        message: `${poiName}签入成功～放心，我也会提醒你签出。`,
    };
};

/**
 * 模拟签出成功响应
 * @param poiId 商家ID
 * @returns 签出响应
 */
export const mockSignOutResponse = (poiId: number) => {
    const poiName = poiNames[getRandomInt(0, poiNames.length - 1)];
    return {
        success: true,
        poiId,
        poiName,
        message: `${poiName}签出成功！`,
    };
};

/**
 * 模拟位置信息
 * @returns 位置信息
 */
export const mockLocationInfo = () => {
    return {
        latitude: 39.9042 + (Math.random() - 0.5) * 0.01,
        longitude: 116.4074 + (Math.random() - 0.5) * 0.01,
        timestamp: Date.now(),
    };
};

/**
 * 模拟气泡弹窗推送 - 签入提醒
 * @returns 签入提醒通知
 */
export const mockSignInNotification = () => {
    return {
        tag: 'popupNotice',
        content: {
            noticeType: 'visitCheckIn',
            poiList: mockRecommendCheckInPois(3),
        },
    };
};

/**
 * 模拟气泡弹窗推送 - 签出提醒
 * @returns 签出提醒通知
 */
export const mockSignOutNotification = () => {
    return {
        tag: 'popupNotice',
        content: {
            noticeType: 'visitCheckOut',
            poiList: [mockRecommendCheckOutPoi()],
        },
    };
};

/**
 * 生成ConfigForm组件的mock数据
 * @returns ConfigForm mock数据
 */
export const mockConfigFormData = () => {
    return {
        type: 'configForm',
        insert: {
            configForm: {
                config: [
                    {
                        label: '提醒频次',
                        type: 'select',
                        options: ['每天', '每周', '每月'],
                        defaultValue: '每天',
                    },
                    {
                        label: '提醒时间',
                        type: 'timeRangePicker',
                        defaultValue: '13:00-18:00',
                    },
                ],
                formId: 'signReminder',
                buttonText: '保存',
            },
        },
    };
};

/**
 * 生成拜访签入提醒设置的ConfigForm mock数据（匹配UI设计）
 * @returns ConfigForm mock数据
 */
export const mockVisitReminderConfigForm = () => {
    return {
        type: 'configForm',
        insert: {
            configForm: {
                config: [
                    {
                        label: '提醒频次',
                        type: 'select',
                        options: ['每天', '每周', '每月', '从不'],
                        defaultValue: '每天',
                    },
                    {
                        label: '提醒时间',
                        type: 'timeRangePicker',
                        defaultValue: '13:00-18:00',
                    },
                ],
                formId: 'signReminder',
                buttonText: '保存',
            },
        },
    };
};

/**
 * 生成包含ConfigForm的完整消息mock数据
 * @returns 完整的消息数据
 */
export const mockConfigFormMessage = () => {
    return {
        msgId: 'config-form-' + Date.now(),
        msgType: 1,
        status: 1,
        currentContent: [mockConfigFormData()],
        history: false,
    };
};

/**
 * 生成拜访签入提醒的ConfigFormMessage mock数据
 * @returns ConfigFormMessage 类型的完整数据
 */
export const mockVisitReminderConfigFormMessage = (): any => {
    return {
        type: 'configForm' as const,
        insert: {
            configForm: {
                config: [
                    {
                        label: '提醒频次',
                        type: 'select' as const,
                        options: ['每天', '每周', '每月', '从不'],
                        defaultValue: '每天',
                    },
                    {
                        label: '提醒时间',
                        type: 'timeRangePicker' as const,
                        defaultValue: '13:00-18:00',
                    },
                ],
                formId: 'signReminder',
                buttonText: '保存',
            },
        },
    };
};

/**
 * 生成通知设置的ConfigFormMessage mock数据
 * @returns ConfigFormMessage 类型的完整数据
 */
export const mockNotificationConfigFormMessage = (): any => {
    return {
        type: 'configForm' as const,
        insert: {
            configForm: {
                config: [
                    {
                        label: '开启通知',
                        labelStyle: 'bold' as const,
                        type: 'switch' as const,
                        defaultValue: 'true',
                    },
                    {
                        label: '通知类型',
                        type: 'select' as const,
                        options: [
                            '推送通知',
                            '短信通知',
                            '邮件通知',
                            '微信通知',
                        ],
                        defaultValue: '推送通知',
                    },
                    {
                        label: '免打扰时间',
                        type: 'timeRangePicker' as const,
                        defaultValue: '22:00-08:00',
                    },
                ],
                formId: 'notificationSettings',
                buttonText: '确定',
            },
        },
    };
};

/**
 * 生成工作时间设置的ConfigFormMessage mock数据
 * @returns ConfigFormMessage 类型的完整数据
 */
export const mockWorkTimeConfigFormMessage = (): any => {
    return {
        type: 'configForm' as const,
        insert: {
            configForm: {
                config: [
                    {
                        label: '启用工作时间限制',
                        labelStyle: 'bold' as const,
                        type: 'switch' as const,
                        defaultValue: 'false',
                    },
                    {
                        label: '工作日',
                        type: 'select' as const,
                        options: [
                            '周一至周五',
                            '周一至周六',
                            '周一至周日',
                            '自定义',
                        ],
                        defaultValue: '周一至周五',
                    },
                    {
                        label: '工作时间',
                        type: 'timeRangePicker' as const,
                        defaultValue: '09:00-18:00',
                    },
                ],
                formId: 'workTimeSettings',
                buttonText: '应用设置',
            },
        },
    };
};

/**
 * 生成数据同步设置的ConfigFormMessage mock数据
 * @returns ConfigFormMessage 类型的完整数据
 */
export const mockDataSyncConfigFormMessage = (): any => {
    return {
        type: 'configForm' as const,
        insert: {
            configForm: {
                config: [
                    {
                        label: '自动同步',
                        labelStyle: 'bold' as const,
                        type: 'switch' as const,
                        defaultValue: 'true',
                    },
                    {
                        label: '同步频率',
                        type: 'select' as const,
                        options: ['实时同步', '每小时', '每天', '手动同步'],
                        defaultValue: '每小时',
                    },
                    {
                        label: '同步时间窗口',
                        type: 'timeRangePicker' as const,
                        defaultValue: '00:00-23:59',
                    },
                ],
                formId: 'dataSyncSettings',
                buttonText: '保存配置',
            },
        },
    };
};

/**
 * 生成所有ConfigFormMessage mock数据的集合
 * @returns 包含多种ConfigFormMessage的数组
 */
export const getAllConfigFormMessages = () => {
    return [
        mockVisitReminderConfigFormMessage(),
        mockNotificationConfigFormMessage(),
        mockWorkTimeConfigFormMessage(),
        mockDataSyncConfigFormMessage(),
    ];
};

/**
 * 随机获取一个ConfigFormMessage mock数据
 * @returns 随机的ConfigFormMessage数据
 */
export const getRandomConfigFormMessage = () => {
    const messages = getAllConfigFormMessages();
    const randomIndex = Math.floor(Math.random() * messages.length);
    return messages[randomIndex];
};

/**
 * 生成包含ConfigFormMessage的完整聊天消息
 * @param configFormMessage ConfigFormMessage数据
 * @param options 可选配置
 * @returns 完整的聊天消息格式
 */
export const createConfigFormChatMessage = (
    configFormMessage: any = mockVisitReminderConfigFormMessage(),
    options: {
        msgId?: string;
        history?: boolean;
        status?: number;
    } = {},
) => {
    return {
        msgId: options.msgId || 'config-form-' + Date.now(),
        msgType: 1,
        status: options.status || 1,
        currentContent: [configFormMessage],
        history: options.history || false,
        timestamp: Date.now(),
    };
};
