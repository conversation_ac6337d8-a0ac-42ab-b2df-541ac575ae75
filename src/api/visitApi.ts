import {
    mockRecommendCheckInPois,
    mockRecommendCheckOutPoi,
    mockUserReminderConfig,
    mockSignInResponse,
    mockSignOutResponse,
    mockLocationInfo,
} from './mockData';

/**
 * 商家信息接口
 */
export interface PoiInfo {
    poiId: number;
    name: string;
    avatar: string;
    distance: string;
    isCheckIn: boolean;
    checkInTime?: number;
}

/**
 * 签出商家信息接口
 */
export interface SignOutPoiInfo {
    poiId: number;
    name: string;
    avatar: string;
    url: string;
}

/**
 * 位置信息接口
 */
export interface LocationInfo {
    latitude: number;
    longitude: number;
    timestamp: number;
}

/**
 * 提醒配置接口
 */
export interface ReminderConfig {
    switch: boolean;
    period: 'daily' | 'workingDays';
    startTime: string;
    endTime: string;
}

/**
 * 获取推荐签入商家列表
 * @param location 位置信息
 * @param forceRefresh 是否强制刷新
 * @returns 商家列表
 */
export const getRecommendCheckInPois = async (
    location?: LocationInfo,
    forceRefresh: boolean = false,
): Promise<PoiInfo[]> => {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 使用mock数据
    return mockRecommendCheckInPois(5);
};

/**
 * 获取推荐签出商家信息
 * @param location 位置信息
 * @returns 签出商家信息
 */
export const getRecommendCheckOutPoi = async (
    location?: LocationInfo,
): Promise<SignOutPoiInfo | null> => {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 随机决定是否需要签出
    const needCheckOut = Math.random() > 0.3;

    if (needCheckOut) {
        return mockRecommendCheckOutPoi();
    }

    return null;
};

/**
 * 获取用户签入提醒配置
 * @returns 用户配置
 */
export const getUserReminderConfig = async (): Promise<ReminderConfig> => {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 300));

    return mockUserReminderConfig();
};

/**
 * 更新用户签入提醒配置
 * @param config 配置信息
 * @returns 是否成功
 */
export const updateUserReminderConfig = async (
    config: ReminderConfig,
): Promise<boolean> => {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 300));

    // 模拟成功
    return true;
};

/**
 * 签入商家
 * @param poiId 商家ID
 * @param location 位置信息
 * @returns 签入结果
 */
export const signInPoi = async (
    poiId: number,
    location?: LocationInfo,
): Promise<{
    success: boolean;
    poiId: number;
    poiName: string;
    message: string;
}> => {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 800));

    return mockSignInResponse(poiId);
};

/**
 * 签出商家
 * @param poiId 商家ID
 * @param location 位置信息
 * @returns 签出结果
 */
export const signOutPoi = async (
    poiId: number,
    location?: LocationInfo,
): Promise<{
    success: boolean;
    poiId: number;
    poiName: string;
    message: string;
}> => {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 800));

    return mockSignOutResponse(poiId);
};

/**
 * 获取当前位置信息
 * @returns 位置信息
 */
export const getCurrentLocation = async (): Promise<LocationInfo> => {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 300));

    return mockLocationInfo();
};

/**
 * 上报位置信息
 * @param location 位置信息
 * @returns 是否成功
 */
export const reportLocation = async (
    location: LocationInfo,
): Promise<boolean> => {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, 200));

    // 模拟成功
    return true;
};
