/**
 * 主页对话接口类型定义
 * 基于接口 GET /bee/v2/bdaiassistant/homePage/dialogue
 */

// API响应基础结构
export interface BaseApiResponse<T = any> {
    code: number;
    data: T;
    msg?: string;
}

// 主页对话接口响应数据结构
export interface HomePageDialogueData {
    skillGroups: SkillGroup[];
    weatherGreeting: WeatherGreeting;
    banner: Banner;
}

// 主页对话接口完整响应结构
export interface HomePageDialogueResponse
    extends BaseApiResponse<HomePageDialogueData> {}

// 技能分组
export interface SkillGroup {
    name: string | null; // 分组名称
    type: 'tiled' | 'group'; // 分组类型
    skills: Skill[]; // 技能列表
    link: string; // name艺术字链接，为空显示name
}

// 技能项
export interface Skill {
    abilityType: number; // 能力类型
    operationType: number; // 操作类型
    content: string; // 显示内容
    subAbilityType: null; // 子能力类型
    url: string; // 跳转URL
    link: string; // 链接地址
    openWay: string; // 打开方式
    isNew: boolean; // 新功能标识
    subSkillList?: Skill[]; // 子技能列表（可选）
}

// 天气问候语
export interface WeatherGreeting {
    greeting: string; // 问候语内容
    weatherTips: string; // 天气提示
    weatherType: 'snow' | 'rain' | 'hot' | 'cold' | 'default'; // 天气类型
}

// Banner配置
export interface Banner {
    bannerContent: string; // Banner内容
    bannerButton: string; // 按钮文字
    triggerQuestion: string; // 触发问题
    type: 'notice' | 'new' | 'default'; // Banner类型
}

// 动作类型枚举
export enum SkillActionType {
    ASK_QUESTION = 'ask', // 发起提问
    OPEN_URL = 'openUrl', // 打开链接
    NAVIGATE = 'navigate', // 路由跳转
    EXPAND_SUB = 'expandSub', // 展开子技能
}

// 技能动作执行参数
export interface SkillActionParams {
    skill: Skill;
    onAsk?: (question: string) => void;
    onOpenUrl?: (url: string) => void;
    onNavigate?: (route: string) => void;
    onExpandSub?: (skill: Skill) => void;
}

// API请求和响应的基础类型
export interface ApiResponse<T = any> {
    data: T;
    success: boolean;
    message?: string;
}

// SWR Hook返回类型
export interface UseHomePageDataReturn {
    homePageData: HomePageDialogueData | undefined;
    skillGroups: SkillGroup[];
    weatherGreeting: WeatherGreeting | undefined;
    banner: Banner | undefined;
    isLoading: boolean;
    error: any;
    refresh: () => void;
}
