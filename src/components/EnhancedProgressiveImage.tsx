import {
    Image,
    ImageProps,
    StyleSheet,
    TouchableOpacity,
    Modal,
    ActivityIndicator,
    View,
    Text,
} from '@mrn/react-native';
import React, { useCallback, useRef, useState } from 'react';
import ImageViewer from 'react-native-image-zoom-viewer';

import Condition from './Condition/Condition';
import ImageLoadingOverlay from './ImageLoadingOverlay';

import NetImages from '@/assets/images/homeRefactor';
import {
    convertWikiPicToS3UrlWithCache,
    shouldConvertImage,
    getFinalImageUrl,
} from '@/utils/imageConverter';

interface EnhancedProgressiveImage extends Omit<ImageProps, 'source'> {
    src?: string;
    imageUrls?: string[];
    defaultImage?: any;
    /** 是否启用自动转换功能，默认为true */
    enableAutoConvert?: boolean;
    /** 转换失败时的回调函数 */
    onConvertFailed?: (originalSrc: string, error?: any) => void;
    /** 转换成功时的回调函数 */
    onConvertSuccess?: (originalSrc: string, newSrc: string) => void;
    /** 是否启用图片预览功能，默认为true */
    enableImageViewer?: boolean;
}

const styles = StyleSheet.create({
    child: {
        backgroundColor: '#eee',
        marginRight: 8,
        marginBottom: 8,
        borderRadius: 4,
        width: 71.5,
        height: 90,
        overflow: 'hidden',
    },
    floatingCloseButton: {
        position: 'absolute',
        bottom: 50,
        left: '50%',
        marginLeft: -25,
        width: 50,
        height: 50,
        borderRadius: 25,
        backgroundColor: '#fff',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
    },
    closeButtonText: {
        color: '#222',
        fontSize: 30,
        fontWeight: 'bold',
    },
});

const EnhancedProgressiveImage = (props: EnhancedProgressiveImage) => {
    const {
        enableAutoConvert = true,
        enableImageViewer = true,
        onConvertFailed,
        onConvertSuccess,
        imageUrls = [],
    } = props;
    const [currentSrc, setCurrentSrc] = useState(props.src);
    const [isConverting, setIsConverting] = useState(false);
    const hasTriedConvertRef = useRef(false);
    const [error, setError] = useState(false);
    const [showImageViewer, setShowImageViewer] = useState(false);
    const allImages = useRef(imageUrls.map((item) => item));
    const currentImageIndex = useRef(
        imageUrls.findIndex((url) => url === props.src),
    );

    // 处理图片加载错误
    const handleImageError = useCallback(async () => {
        const originalSrc = currentSrc;

        // 只有学城wiki图片才需要转换
        if (
            !shouldConvertImage(originalSrc) ||
            !originalSrc ||
            !enableAutoConvert
        ) {
            setError(true);
            return;
        }

        if (hasTriedConvertRef.current || isConverting) {
            return;
        }
        setIsConverting(true);
        hasTriedConvertRef.current = true;

        try {
            const newSrc = await convertWikiPicToS3UrlWithCache(originalSrc);

            if (newSrc && newSrc !== originalSrc) {
                setCurrentSrc(newSrc);
                onConvertSuccess && onConvertSuccess(originalSrc, newSrc);
            } else {
                setError(true);
                onConvertFailed && onConvertFailed(originalSrc);
            }
        } catch (error) {
            setError(true);
            onConvertFailed && onConvertFailed(originalSrc, error);
        } finally {
            setIsConverting(false);
        }
    }, [
        currentSrc,
        enableAutoConvert,
        isConverting,
        onConvertFailed,
        onConvertSuccess,
    ]);

    // 每次点击时，更新预览队列
    const onImagePress = useCallback(() => {
        allImages.current = allImages.current.map((url) =>
            getFinalImageUrl(url),
        );
        setShowImageViewer(true);
    }, [currentSrc, enableImageViewer, error]);

    const handleCloseImageViewer = useCallback(() => {
        setShowImageViewer(false);
    }, []);

    return (
        <>
            <Condition condition={[!isConverting, isConverting]}>
                <TouchableOpacity onPress={onImagePress} style={styles.child}>
                    <Image
                        {...props}
                        onError={handleImageError}
                        source={{
                            uri: error
                                ? props.defaultImage || NetImages.defaultImage
                                : currentSrc,
                        }}
                    />
                </TouchableOpacity>
                <ImageLoadingOverlay visible={true} />
            </Condition>

            {enableImageViewer && (
                <Modal
                    visible={showImageViewer}
                    transparent={true}
                    onRequestClose={handleCloseImageViewer}
                >
                    <View style={{ flex: 1 }}>
                        <ImageViewer
                            imageUrls={allImages.current.map((url) => ({
                                url,
                            }))}
                            index={currentImageIndex.current || 0}
                            onCancel={handleCloseImageViewer}
                            enableSwipeDown={true}
                            saveToLocalByLongPress={false}
                            onClick={handleCloseImageViewer}
                            loadingRender={() => (
                                <ActivityIndicator color="white" size="small" />
                            )}
                        />

                        {/* 悬浮关闭按钮 */}
                        <TouchableOpacity
                            style={styles.floatingCloseButton}
                            onPress={handleCloseImageViewer}
                            activeOpacity={0.8}
                        >
                            <Text style={styles.closeButtonText}>×</Text>
                        </TouchableOpacity>
                    </View>
                </Modal>
            )}
        </>
    );
};

export default EnhancedProgressiveImage;
