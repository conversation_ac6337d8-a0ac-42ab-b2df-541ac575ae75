import { openPage } from '@mfe/bee-foundation-utils';
import WebView from '@mrn/mrn-webview';
import { NativeSyntheticEvent } from '@mrn/react-native';
import { View, StyleSheet } from '@mrn/react-native';
import { Toast } from '@roo/roo-rn';
import React, { useRef, useState, useEffect } from 'react';

import { createRejectHtml } from './createRejectHtml';
import { useSendMessage } from '../hooks/useSendMessage';
import { EntryPoint, EntryPointType } from '../types';
import { RejectCardMessage } from '../types/message';

interface Props {
    data: RejectCardMessage['insert']['rejectCard'];
    onEndTyping?: () => void;
}

const styles = StyleSheet.create({
    container: {
        marginVertical: 8,
    },
    webview: {
        backgroundColor: 'transparent',
    },
});

const RejectCardWebView: React.FC<Props> = ({ data, onEndTyping }) => {
    const { send } = useSendMessage();
    const webViewRef = useRef<WebView>(null);
    const [webViewHeight, setWebViewHeight] = useState(200);

    useEffect(() => {
        onEndTyping?.();
    }, [onEndTyping]);

    // 处理来自 WebView 的消息
    const handleMessage = (event: NativeSyntheticEvent<any>) => {
        try {
            const messageData = JSON.parse(event.nativeEvent.data);

            switch (messageData.type) {
                case 'heightChanged':
                    // 更新WebView高度
                    const newHeight = Math.max(messageData.payload + 20, 100);
                    setWebViewHeight(newHeight);
                    break;

                case 'submitQuestion':
                    // 处理提问操作
                    if (messageData.payload) {
                        send(
                            messageData.payload,
                            EntryPointType.USER,
                            EntryPoint.reject_card,
                        );
                    }
                    break;

                case 'navigate':
                    // 处理URL跳转
                    if (messageData.payload) {
                        try {
                            openPage(messageData.payload);
                        } catch (error) {
                            console.error('Navigate error:', error);
                            Toast.open('跳转失败，请重试');
                        }
                    }
                    break;

                default:
                    console.log('Unknown message type:', messageData.type);
                    break;
            }
        } catch (error) {
            console.error('Failed to parse WebView message:', error);
        }
    };

    const [htmlContent, setHtmlContent] = useState('');
    useEffect(() => {
        (async () => {
            const html = await createRejectHtml(data);
            setHtmlContent(html);
        })();
    }, [data]);

    return (
        <View style={styles.container}>
            <WebView
                ref={webViewRef}
                source={{ html: htmlContent }}
                style={[styles.webview, { height: webViewHeight }]}
                onMessage={handleMessage}
                javaScriptEnabled={true}
                domStorageEnabled={true}
                scrollEnabled={false}
                showsVerticalScrollIndicator={false}
                originWhitelist={['*']}
                mixedContentMode="always"
            />
        </View>
    );
};

export default RejectCardWebView;
