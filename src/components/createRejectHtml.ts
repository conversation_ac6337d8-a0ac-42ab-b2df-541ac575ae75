import { RejectCardMessage } from '@/types/message';

// 远程模板 URL
const REMOTE_TEMPLATE_URL =
    'https://s3plus.meituan.net/bdaiassistant-public/rn_assets/templates/rejectCard.html?v=1.0.1';

// 获取远程 HTML 模板
export const fetchRemoteTemplate = async (): Promise<string> => {
    try {
        const response = await fetch(REMOTE_TEMPLATE_URL);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const htmlContent = await response.text();
        return htmlContent;
    } catch (error) {
        console.error('Failed to fetch remote template:', error);
        throw error;
    }
};

// 获取远程模板并注入数据
export const createRejectHtmlFromRemote = async (
    data: RejectCardMessage['insert']['rejectCard'],
): Promise<string> => {
    try {
        const remoteHtml = await fetchRemoteTemplate();
        // 注入通信器到远程模板
        const finalHtml = injectCommunicator(remoteHtml, data);
        return finalHtml;
    } catch (error) {
        console.error('Failed to create reject HTML from remote:', error);
        // 降级到本地模板
        return '';
    }
};

// 创建通信器 - 独立可注入模块（包含高度通信和业务通信）
const createCommunicator = (dataJson: string) => {
    return `
        window.data = ${dataJson};
        // 通信器 - 独立可注入模块
        (function() {
            const Communicator = {
                // 环境检测
                isMRNEnvironment: function() {
                    return typeof window.MRNWebView !== 'undefined' && window.MRNWebView.postMessage;
                },

                // 统一的消息发送方法
                postMessage: function(data) {
                    if (this.isMRNEnvironment()) {
                        // MRN环境使用MRNWebView.postMessage
                        window.MRNWebView.postMessage(
                            typeof data === 'string' ? data : JSON.stringify(data)
                        );
                    } else {
                        // 非MRN环境使用原生postMessage
                        window.parent.postMessage(data, '*');
                    }
                },

                // 通知父窗口高度变化
                notifyHeightChange: function() {
                    const height = document.body.scrollHeight;
                    this.postMessage({
                        type: 'heightChanged',
                        payload: height
                    });
                },

                // 在DOM变化后延迟通知高度变化
                delayNotifyHeightChange: function(delay = 0) {
                    setTimeout(this.notifyHeightChange.bind(this), delay);
                },

                // 监听DOM变化并自动通知高度变化
                observeHeightChanges: function() {
                    if (typeof ResizeObserver !== 'undefined') {
                        const observer = new ResizeObserver(() => {
                            this.delayNotifyHeightChange(50); // 添加小延迟避免过度频繁
                        });
                        observer.observe(document.body);
                    }

                    // 监听DOM内容变化
                    if (typeof MutationObserver !== 'undefined') {
                        const mutationObserver = new MutationObserver(() => {
                            this.delayNotifyHeightChange(50);
                        });
                        mutationObserver.observe(document.body, {
                            childList: true,
                            subtree: true,
                            attributes: true
                        });
                    }
                },

                // 业务通信方法
                submitQuestion: function(question) {
                    console.log('Action: submitQuestion, Question:', question);
                    this.postMessage({
                        type: 'submitQuestion',
                        payload: question
                    });
                },

                navigate: function(url) {
                    console.log('Action: navigate, URL:', url);
                    this.postMessage({
                        type: 'navigate',
                        payload: url
                    });
                },

                // 提供给业务代码调用的接口
                triggerHeightUpdate: function() {
                    this.delayNotifyHeightChange();
                },

                // 初始化通信器
                init: function(initialDelay = 100) {
                    this.delayNotifyHeightChange(initialDelay);
                    this.observeHeightChanges();

                    // 重写全局postMessage方法以支持MRN环境
                    if (this.isMRNEnvironment()) {
                        window.postMessage = (data) => {
                            this.postMessage(data);
                        };
                    }
                }
            };

            // 将通信器暴露到全局，供业务代码使用
            window.Communicator = Communicator;
            // 保持向后兼容
            window.HeightCommunicator = {
                triggerHeightUpdate: Communicator.triggerHeightUpdate.bind(Communicator)
            };

            // 如果DOM已经加载完成，立即初始化，否则等待
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', function() {
                    Communicator.init();
                });
            } else {
                Communicator.init();
            }
        })();
    `;
};

// 注入通信器到HTML内容
const injectCommunicator = (
    htmlContent: string,
    data: RejectCardMessage['insert']['rejectCard'],
) => {
    const dataJson = JSON.stringify(data);
    const communicatorScript = createCommunicator(dataJson);

    // 如果HTML中已经有<script>标签，在第一个<script>前注入
    // 否则在</body>前注入
    if (htmlContent.includes('<script>')) {
        const firstScriptIndex = htmlContent.indexOf('<script>');
        return (
            htmlContent.slice(0, firstScriptIndex) +
            `<script>${communicatorScript}</script>\n    ` +
            htmlContent.slice(firstScriptIndex)
        );
    } else if (htmlContent.includes('<body>')) {
        return htmlContent.replace(
            '<body>',
            `<body>\n<script>${communicatorScript}</script>\n`,
        );
    } else {
        // 如果没有</body>标签，直接在末尾添加
        return `<script>${communicatorScript}</script>` + htmlContent;
    }
};

// 创建HTML模板字符串 - 移除外部依赖，优化加载速度
export const createRejectHtml = (
    data: RejectCardMessage['insert']['rejectCard'],
) => {
    return fetchRemoteTemplate().then((html) => {
        const finalHtml = injectCommunicator(html, data);
        return finalHtml;
    });
};
