import { useSafeAreaInsets } from '@mfe/bee-foundation-navigation';
import { openPage } from '@mfe/bee-foundation-utils';
import {
    Animated,
    Image,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from '@mrn/react-native';
import { useDebounceFn } from 'ahooks';
import React, { useContext } from 'react';

import ClosePng from '../../../assets/images/close.png';
import RootTagContext from '../../../hooks/rootTagContext';
import AssistantIcon from '../../AssistantIcon';

interface ChatHeader {
    scrollHeight?: Animated.Value;
    navigator: { pop: () => void };
}

const SCROLL_HEIGHT = 73;

const styles = StyleSheet.create({
    container: {
        position: 'relative',
        marginTop: 14.5,
    },
    closeCircle: {
        height: 24,
        width: 24,
        borderRadius: 12,
        backgroundColor: 'rgba(255, 255, 255, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    complex: {
        paddingHorizontal: 16,
        zIndex: 10,
        flexDirection: 'row',
    },
    simple: {
        paddingHorizontal: 16,
        flexDirection: 'row',
        justifyContent: 'flex-end',
        paddingBottom: 12,
        left: 0,
        right: 0,
    },
    hi: {
        fontWeight: '900',
        fontSize: 16,
        lineHeight: 18,
    },
    title: {
        fontWeight: '600',
        fontSize: 14,
        lineHeight: 18,
    },
    text: {
        fontSize: 11,
        lineHeight: 15,
        marginTop: 2,
    },
    underline: {
        textDecorationLine: 'underline',
    },
    noticeText: {
        color: '#666',
        textAlign: 'center',
    },
    link: {
        color: '#FF6A00',
    },
    headMid: {
        justifyContent: 'center',
        marginLeft: 8,
        flex: 1,
    },
    image: {
        height: 24,
        width: 24,
    },
});

const ChatHeader = (props: ChatHeader) => {
    const insets = useSafeAreaInsets();

    const { pop } = useContext(RootTagContext);

    const statusBarHeight = StatusBar.currentHeight || insets.top;

    const opacitySimple = props.scrollHeight.interpolate({
        inputRange: [0, SCROLL_HEIGHT],
        outputRange: [1, 0],
    });

    const opacityComplex = props.scrollHeight.interpolate({
        inputRange: [0, SCROLL_HEIGHT],
        outputRange: [0, 1],
    });

    const { run: onClose } = useDebounceFn(
        () => {
            pop();
        },
        { wait: 200 },
    );

    const gotoInstruction = () => {
        openPage(
            'meituanwaimaibee://beewaimai.meituan.com/mrn?mrn_biz=waimaicrm&mrn_entry=bee-assistant-main&mrn_component=bee-assistant&initialRoute=Instruction',
        );
    };

    return (
        <View style={[styles.container]}>
            <Animated.View
                style={[
                    styles.simple,
                    { opacity: opacitySimple, position: 'absolute' },
                ]}
            >
                <TouchableOpacity style={styles.closeCircle}>
                    <Image source={ClosePng} style={styles.image} />
                </TouchableOpacity>
            </Animated.View>
            <Animated.View
                style={{
                    opacity: opacityComplex,
                }}
            >
                <View
                    style={[
                        styles.complex,
                        {
                            marginTop: -statusBarHeight - 12,
                            paddingTop: statusBarHeight + 12,
                            paddingBottom: 12,
                        },
                    ]}
                >
                    <AssistantIcon size={40} noText />
                    <View style={styles.headMid}>
                        <View style={{ flexDirection: 'row' }}>
                            <Text style={styles.hi}>Hi,</Text>
                            <Text style={styles.title}>我是小蜜智能助手</Text>
                        </View>
                        <Text style={styles.text} onPress={gotoInstruction}>
                            点击查看
                            <Text style={styles.underline}>使用说明</Text>
                            ，期待您的反馈
                        </Text>
                    </View>
                    <TouchableOpacity
                        style={styles.closeCircle}
                        onPress={onClose}
                    >
                        <Image source={ClosePng} style={styles.image} />
                    </TouchableOpacity>
                </View>
            </Animated.View>
        </View>
    );
};

ChatHeader.defaultProps = {
    scrollHeight: 0,
};

export default ChatHeader;
