import {
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
    Image,
} from '@mrn/react-native';
import _ from 'lodash';
import React, { FC } from 'react';

import useHotQuestions from '../../../hooks/useHotQuestions';

import Condition from '@/components/Condition/Condition';
import { Bubble } from '@/components/MessageBox/Answer/AnswerContent/SelectionMessage/Bubble';
import { useBizInfo } from '@/hooks/useBizInfo';
import useOptionPress from '@/hooks/useOptionPress';
import useTrace from '@/hooks/useTrace';
import TWS from '@/TWS';
import { EntryPoint } from '@/types';

interface HotQuestionsProps {}

const HotQuestions: FC<HotQuestionsProps> = () => {
    const { hotQuestions, loading, error } = useHotQuestions();
    const trace = useTrace();
    const { bizId } = useBizInfo();

    const { onOptionPress } = useOptionPress({
        msgId: undefined,
        msgType: undefined,
        history: false,
    });
    if (loading) {
        return null;
    }

    if (error || hotQuestions.length === 0) {
        return null;
    }

    return (
        <View style={styles.container}>
            <View style={styles.questionList}>
                {_.chunk(hotQuestions, 2).map((chunk, index) => (
                    <View
                        style={{
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            paddingHorizontal: 10,
                        }}
                        key={`hot-question-${index}`}
                    >
                        {chunk.map((question: any, questionIndex) => (
                            <TouchableOpacity
                                key={`hot-question-${questionIndex}`}
                                style={[
                                    styles.questionItem,
                                    {
                                        marginRight:
                                            questionIndex % 2 === 0 ? 0 : 10,
                                    },
                                ]}
                                onPress={() => {
                                    trace(
                                        'app_homepage_hot_question',
                                        'trigger',
                                        JSON.stringify({
                                            question: question.content,
                                            bizId,
                                        }),
                                    );
                                    onOptionPress(
                                        question,
                                        [
                                            `${EntryPoint.option_list}${
                                                questionIndex + 1
                                            }`,
                                        ]
                                            .filter(Boolean)
                                            .join('-'),
                                    );
                                }}
                            >
                                {question.link ? (
                                    <View style={styles.questionIcon}>
                                        <Image
                                            source={{ uri: question.link }}
                                            style={[
                                                TWS.square(15),
                                                {
                                                    position: 'relative',
                                                    top: -2,
                                                },
                                            ]}
                                        />
                                    </View>
                                ) : null}
                                <Text style={[styles.questionText]}>
                                    {question.content}
                                </Text>
                                <Condition condition={[question.isNew]}>
                                    <Bubble
                                        style={{
                                            position: 'relative',
                                            top: -8,
                                            marginLeft: 2,
                                        }}
                                    />
                                </Condition>
                            </TouchableOpacity>
                        ))}
                    </View>
                ))}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        paddingHorizontal: 16,
        marginVertical: 12,
    },
    title: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 12,
        color: '#333333',
    },
    questionList: {},
    questionItem: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#fff',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 16,
        marginBottom: 8,
        height: 44,
        marginRight: 10,
        marginLeft: 10,
        flex: 1,
    },
    questionItemHighlight: {
        backgroundColor: '#FFF4E8',
    },
    questionIcon: {
        marginRight: 4,
        width: 11,
        height: 11,
    },
    questionText: {
        fontSize: 13,
        color: '#333333',
        maxWidth: 9 * 14 * 1.1,
    },
    questionTextHighlight: {
        color: '#FF6A0F',
    },
    newTag: {
        backgroundColor: '#FF6A0F',
        borderRadius: 2,
        paddingHorizontal: 4,
        marginLeft: 4,
    },
    newTagText: {
        color: '#FFFFFF',
        fontSize: 10,
    },
    errorContainer: {
        alignItems: 'center',
        justifyContent: 'center',
        padding: 16,
    },
    errorText: {
        fontSize: 14,
        color: '#999999',
        marginBottom: 8,
    },
    refreshButton: {
        backgroundColor: '#EEEEEE',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 4,
    },
    refreshText: {
        fontSize: 14,
        color: '#333333',
    },
});

export default HotQuestions;
