import { AccessibilityInfo, Platform } from '@mrn/react-native';

/**
 * 无障碍增强工具
 * 提供无障碍功能的增强和优化
 */
export class AccessibilityEnhancement {
    /**
     * 检查是否启用了屏幕阅读器
     */
    static async isScreenReaderEnabled(): Promise<boolean> {
        try {
            return await AccessibilityInfo.isScreenReaderEnabled();
        } catch (error) {
            console.warn('Failed to check screen reader status:', error);
            return false;
        }
    }

    /**
     * 发布无障碍公告
     * @param message 公告内容
     */
    static announceForAccessibility(message: string): void {
        try {
            AccessibilityInfo.announceForAccessibility(message);
        } catch (error) {
            console.warn('Failed to announce for accessibility:', error);
        }
    }

    /**
     * 获取推荐的最小触摸目标尺寸
     * iOS: 44x44pt, Android: 48x48dp
     */
    static getMinTouchTarget() {
        return {
            width: Platform.OS === 'ios' ? 44 : 48,
            height: Platform.OS === 'ios' ? 44 : 48,
        };
    }

    /**
     * 创建无障碍属性对象
     */
    static createAccessibilityProps(options: {
        label?: string;
        hint?: string;
        role?: string;
        state?: { disabled?: boolean; selected?: boolean };
        value?: { min?: number; max?: number; now?: number };
    }) {
        const { label, hint, role, state, value } = options;

        return {
            accessible: true,
            accessibilityLabel: label,
            accessibilityHint: hint,
            accessibilityRole: role as any,
            accessibilityState: state,
            accessibilityValue: value,
        };
    }
}

/**
 * 深色模式支持工具
 */
export class DarkModeSupport {
    /**
     * 根据深色模式返回对应的颜色
     */
    static getColor(
        lightColor: string,
        darkColor: string,
        isDark = false,
    ): string {
        return isDark ? darkColor : lightColor;
    }

    /**
     * 获取适配深色模式的通用颜色方案
     */
    static getColorScheme(isDark = false) {
        return {
            background: this.getColor('#FFFFFF', '#1C1C1E', isDark),
            secondaryBackground: this.getColor('#F8F9FA', '#2C2C2E', isDark),
            text: this.getColor('#333333', '#FFFFFF', isDark),
            secondaryText: this.getColor('#666666', '#AAAA AA', isDark),
            border: this.getColor('#E5E5E5', '#3A3A3C', isDark),
            primary: '#FF6A00', // 主色调保持一致
            success: this.getColor('#52C41A', '#52C41A', isDark),
            warning: this.getColor('#FAAD14', '#FAAD14', isDark),
            error: this.getColor('#F5222D', '#F5222D', isDark),
        };
    }
}

/**
 * 响应式布局工具
 */
export class ResponsiveLayout {
    /**
     * 根据屏幕尺寸返回响应式数值
     */
    static responsive(
        config: {
            small?: number; // < 375
            medium?: number; // 375-414
            large?: number; // > 414
        },
        screenWidth: number,
    ): number {
        const { small = 0, medium = small, large = medium } = config;

        if (screenWidth < 375) {
            return small;
        } else if (screenWidth <= 414) {
            return medium;
        } else {
            return large;
        }
    }

    /**
     * 获取适配不同屏幕的字体大小
     */
    static getFontSize(
        size: 'small' | 'medium' | 'large' | 'xlarge',
        screenWidth: number,
    ): number {
        const baseSizes = {
            small: 12,
            medium: 14,
            large: 16,
            xlarge: 18,
        };

        const scale = this.responsive(
            { small: 0.9, medium: 1, large: 1.1 },
            screenWidth,
        );
        return Math.round(baseSizes[size] * scale);
    }

    /**
     * 获取适配不同屏幕的间距
     */
    static getSpacing(
        size: 'xs' | 'sm' | 'md' | 'lg' | 'xl',
        screenWidth: number,
    ): number {
        const baseSizes = {
            xs: 4,
            sm: 8,
            md: 16,
            lg: 24,
            xl: 32,
        };

        const scale = this.responsive(
            { small: 0.8, medium: 1, large: 1.2 },
            screenWidth,
        );
        return Math.round(baseSizes[size] * scale);
    }
}

/**
 * 触摸反馈增强
 */
export class TouchFeedback {
    /**
     * 获取标准的触摸反馈属性
     */
    static getStandardProps() {
        return {
            activeOpacity: 0.7,
            delayPressIn: 0,
            delayPressOut: 100,
        };
    }

    /**
     * 获取按钮的触摸反馈属性
     */
    static getButtonProps() {
        return {
            ...this.getStandardProps(),
            activeOpacity: 0.8,
        };
    }

    /**
     * 获取列表项的触摸反馈属性
     */
    static getListItemProps() {
        return {
            ...this.getStandardProps(),
            activeOpacity: 0.6,
        };
    }
}

// 默认导出工具类集合
export default {
    AccessibilityEnhancement,
    DarkModeSupport,
    ResponsiveLayout,
    TouchFeedback,
};
