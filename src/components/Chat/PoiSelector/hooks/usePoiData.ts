import { usePagination } from 'ahooks';
import { useMemo } from 'react';

import { getPoiListByPage } from '../api/poiApi';

/**
 * 自定义 Hook：管理 POI 数据获取和分页
 * @param searchValue 搜索关键词
 * @param filter 筛选器字符串
 * @param tag tag类型
 * @returns 数据和分页控制器
 */
export const usePoiData = (
    searchValue: string,
    filter?: string,
    tag?: string,
) => {
    // 使用useMemo稳定filter引用，避免不必要的重新请求
    const stableFilter = useMemo(() => filter, [filter]);
    const { data, pagination, loading } = usePagination(
        async ({ current, pageSize = 10 }) => {
            const resData = await getPoiListByPage(
                current,
                pageSize,
                searchValue,
                stableFilter,
                tag,
            );
            return resData
                ? {
                      list: [
                          ...(current === 1 ? [] : data?.list || []),
                          ...resData.list,
                      ],
                      total: resData.total,
                  }
                : data;
        },
    );

    return {
        data,
        pagination,
        poiList: data?.list,
        loading,
    };
};
