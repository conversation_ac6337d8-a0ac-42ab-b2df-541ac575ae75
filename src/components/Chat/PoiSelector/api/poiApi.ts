import { apiCaller } from '@mfe/cc-api-caller-bee';

import {
    ApiResponse,
    GetPoiListResponse,
    PaginationData,
} from '../types/PoiSelector';

/**
 * 分页获取 POI 列表
 * @param pageNum 页码
 * @param pageSize 每页大小
 * @param keyword 搜索关键词
 * @param filter 筛选器字符串
 * @param type tag类型
 * @returns POI 列表数据
 */
export const getPoiListByPage = async (
    pageNum: number,
    pageSize = 10,
    keyword = undefined,
    filter?: string,
    type?: string,
): Promise<PaginationData | null> => {
    const requestData: any = {
        pageSize: pageSize as any,
        pageNum,
        data: keyword,
    };

    if (filter) {
        requestData.filter = filter;
    }

    if (type) {
        requestData.type = type;
    }

    const res: ApiResponse<GetPoiListResponse> = await apiCaller.get(
        '/bee/v1/bdaiassistant/common/getOwnPoiListByPage',
        requestData,
    );

    if (res.code === 0) {
        return {
            list: res.data?.poiList || [],
            total: res.data?.total || 0,
        };
    }

    return null;
};
