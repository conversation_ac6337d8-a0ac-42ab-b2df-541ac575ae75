import {
    View,
    Text,
    Image,
    TouchableOpacity,
    StyleSheet,
} from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import React from 'react';

import TWS from '../../../../TWS';
import Condition from '../../../Condition/Condition';
import { PoiItemProps } from '../types/PoiSelector';

/**
 * POI 项目组件
 */
export const PoiItem = ({
    onPress,
    onSign,
    isLast,
    id,
    name,
    url,
    online,
    labels,
    tags,
    isMultiSelect,
    isSelected,
    onToggleSelect,
    tagNameList,
    distance,
}: PoiItemProps) => {
    const TheView: any = onPress ? TouchableOpacity : View;

    return (
        <TheView
            onPress={isMultiSelect ? onToggleSelect : onPress}
            style={[
                TWS.row(),
                {
                    borderRadius: 10,
                    padding: 12,
                    marginVertical: 5,
                    borderBottomColor: !isLast ? '#E7E8E9' : 'transparent',
                    borderBottomWidth: StyleSheet.hairlineWidth,
                },
            ]}
        >
            {isMultiSelect && (
                <View
                    style={{
                        marginRight: 8,
                        justifyContent: 'center',
                    }}
                >
                    <View
                        style={[
                            TWS.square(16),
                            {
                                borderRadius: 4,
                                borderWidth: 1,
                                borderColor: isSelected ? '#FFD100' : '#E7E8E9',
                                backgroundColor: isSelected
                                    ? '#FFD100'
                                    : '#fff',
                                justifyContent: 'center',
                                alignItems: 'center',
                            },
                        ]}
                    >
                        {isSelected && (
                            <Icon type="check" size={8} tintColor="#222" />
                        )}
                    </View>
                </View>
            )}
            <View
                style={{
                    borderRadius: 8,
                    marginRight: 8,
                    overflow: 'hidden',
                }}
            >
                <Image
                    source={{ uri: url }}
                    style={[TWS.square(36), { borderRadius: 8 }]}
                />
            </View>

            <View style={{ flex: 1 }}>
                <View
                    style={[
                        TWS.row(),
                        { flexWrap: 'wrap', alignItems: 'center' },
                    ]}
                >
                    {tagNameList?.map((v) => (
                        <View
                            style={{
                                backgroundColor: '#FFF5F6',
                                paddingHorizontal: 4,
                                maxWidth: 100,
                                justifyContent: 'center',
                                marginRight: 4,
                                borderRadius: 2,
                                height: 16,
                            }}
                        >
                            <Text
                                style={{
                                    color: '#FF192D',
                                    fontSize: 11,
                                }}
                                numberOfLines={1}
                            >
                                {v}
                            </Text>
                        </View>
                    ))}
                    <Text
                        numberOfLines={1}
                        ellipsizeMode={'middle'}
                        style={{
                            color: '#222',
                            fontSize: 16,
                            flex: 1,
                        }}
                    >
                        {name}
                    </Text>
                    <Condition condition={[online !== undefined]}>
                        <View
                            style={[
                                TWS.row(),
                                TWS.center(),
                                {
                                    borderRadius: 8,
                                    backgroundColor: online
                                        ? '#E6FAF2'
                                        : '#F1F1F1',
                                    paddingHorizontal: 4,
                                    paddingVertical: 2,
                                },
                            ]}
                        >
                            <View
                                style={[
                                    TWS.circle(8),
                                    {
                                        backgroundColor: online
                                            ? '#58C080'
                                            : '#858688',
                                        marginRight: 4,
                                    },
                                ]}
                            />
                            <Text style={{ fontSize: 10 }}>
                                {online ? '在线' : '下线'}
                            </Text>
                        </View>
                    </Condition>
                </View>

                <View style={{ display: 'flex', flexDirection: 'row' }}>
                    <Condition condition={[Boolean(id)]}>
                        <Text
                            style={{
                                color: '#666',
                                fontSize: 14,
                            }}
                        >
                            ID:{id}
                        </Text>
                    </Condition>
                    <Text
                        style={{
                            paddingLeft: 9,
                            color: '#666',
                            fontSize: 14,
                        }}
                    >
                        距离{distance ? distance + 'm' : '未知'}
                    </Text>
                </View>
                <View
                    style={{
                        flexWrap: 'wrap',
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        marginTop: 2,
                    }}
                >
                    {labels?.map((v) => (
                        <Text style={{ color: '#666' }} key={String(v.value)}>
                            {v.label}:{v.value}
                        </Text>
                    ))}
                    {tags?.map((v) => (
                        <View
                            style={{
                                borderColor: '#666',
                                borderWidth: 0.5,
                                borderRadius: 4,
                                paddingHorizontal: 4,
                                paddingVertical: 2,
                            }}
                            key={v}
                        >
                            <Text style={{ fontSize: 10 }}>{v}</Text>
                        </View>
                    ))}
                </View>
            </View>
            <View>
                <TouchableOpacity
                    onPress={onSign}
                    style={{
                        backgroundColor: '#4021FF',
                        borderRadius: 20,
                        paddingVertical: 9,
                        paddingHorizontal: 14,
                    }}
                >
                    <Text
                        style={{
                            color: '#fff',
                            fontSize: 14,
                            fontWeight: '500',
                        }}
                    >
                        签入
                    </Text>
                </TouchableOpacity>
            </View>
        </TheView>
    );
};
