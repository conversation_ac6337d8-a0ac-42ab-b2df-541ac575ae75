import { View, Text } from '@mrn/react-native';
import { Button } from '@roo/roo-rn';
import React from 'react';

interface ConfirmButtonProps {
    selectedCount: number;
    onConfirm: () => void;
}

/**
 * 确认按钮组件
 */
export const ConfirmButton = ({
    selectedCount,
    onConfirm,
}: ConfirmButtonProps) => (
    <View
        style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 12,
            paddingHorizontal: 12,
        }}
    >
        <Text
            style={{
                color: '#222',
                fontSize: 14,
                fontWeight: '400',
            }}
        >
            已选{selectedCount}/可选30
        </Text>
        <Button
            type="primary"
            onPress={onConfirm}
            disabled={selectedCount === 0}
            style={[
                {
                    backgroundColor: selectedCount > 0 ? '#FFD100' : '#E7E8E9',
                    borderRadius: 20,
                    paddingVertical: 12,
                    alignItems: 'center',
                    width: 210,
                },
            ]}
        >
            <Text
                style={{
                    color: selectedCount > 0 ? '#333' : '#999',
                    fontSize: 16,
                    fontWeight: '500',
                }}
            >
                确定
            </Text>
        </Button>
    </View>
);
