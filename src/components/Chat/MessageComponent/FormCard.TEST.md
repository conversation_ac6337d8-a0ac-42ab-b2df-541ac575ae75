# FormCard 功能测试指南

## 测试环境设置

### 方法一：使用Mock测试页面

1. 在开发环境中运行项目：
```bash
npm run dev
```

2. 导航到测试页面（如果已添加路由）或在控制台使用：
```javascript
// 在浏览器控制台中执行
const mockMode = require('./src/hooks/mock/useMockMode').default();

// 测试不同功能
mockMode.testFormCardBasic();       // 基础功能
mockMode.testFormCardTextarea();    // 多行文本
mockMode.testFormCardSelect();      // 下拉选择
mockMode.testFormCardValidation();  // 校验功能
mockMode.testFormCardHideSpan();    // 隐藏按钮
mockMode.testFormCardComprehensive(); // 综合测试
```

### 方法二：直接在聊天页面测试

修改 `mockData.ts` 中的默认数据源，将第29行改为：
```typescript
const mockData = mockServiceData.formCardBasic as any; // 改为你想测试的数据
```

## 功能测试清单

### ✅ 1. 基础功能测试 (formCardBasic)
**测试内容**：
- 基础的input输入框
- radio单选按钮组
- 基础表单提交

**测试步骤**：
1. 输入BDmis账号
2. 选择绩效目标
3. 点击"提交基础信息"按钮
4. 验证表单数据提交成功

### ✅ 2. 多行文本测试 (formCardTextarea)
**测试内容**：
- textarea多行输入框
- 长度校验（minLength, maxLength）
- 行数控制

**测试步骤**：
1. 在"问题描述"中输入少于10个字符，验证最小长度校验
2. 输入超过500个字符，验证最大长度校验
3. 输入正确长度的内容，验证通过
4. 测试多行文本输入和显示

### ✅ 3. Select下拉选择测试 (formCardSelect)
**测试内容**：
- select下拉选择框
- Dialog弹窗选择
- 多选项支持

**测试步骤**：
1. 点击"城市选择"下拉框，验证弹窗出现
2. 选择不同选项，验证选中状态更新
3. 点击"取消"验证取消操作
4. 测试默认值显示

### ✅ 4. 校验功能测试 (formCardValidation)
**测试内容**：
- 正则表达式校验
- 长度校验组合
- 必填/可选字段
- 自定义错误消息

**测试步骤**：
1. **用户名测试**：
   - 输入少于3个字符，验证长度校验
   - 输入特殊字符，验证正则校验
   - 输入正确格式，验证通过

2. **手机号测试**：
   - 输入错误格式，验证正则校验
   - 输入正确的11位手机号，验证通过

3. **邮箱测试**（可选）：
   - 留空，验证可选字段
   - 输入错误格式，验证邮箱格式校验
   - 输入正确邮箱，验证通过

4. **个人简介测试**：
   - 输入超过200字符，验证长度限制

### ✅ 5. HideSpan测试 (formCardHideSpan)
**测试内容**：
- 隐藏提交按钮
- 自定义提交逻辑
- 表单状态管理

**测试步骤**：
1. 验证页面不显示提交按钮
2. 输入内容，验证表单仍可正常交互
3. 测试select选择功能正常

### ✅ 6. 综合功能测试 (formCardComprehensive)
**测试内容**：
- 所有新功能组合使用
- 复杂表单场景
- tooltip提示功能

**测试步骤**：
1. **完整表单填写**：
   - 测试所有字段类型：input、textarea、select、radio
   - 验证必填字段校验
   - 测试tooltip提示（点击问号图标）

2. **校验组合测试**：
   - 同时触发多个校验错误
   - 验证错误消息显示
   - 修正错误后重新提交

3. **交互体验测试**：
   - 测试长表单滚动
   - 验证输入焦点管理
   - 测试选择器弹窗层级

## 历史消息测试

### 仅最新回答可操作功能测试

**测试步骤**：
1. 提交一个表单
2. 发送新的消息获得新的表单
3. 验证历史表单被禁用（输入框不可编辑，按钮隐藏）
4. 验证最新表单可正常操作

## 常见问题排查

### 1. 表单不显示
- 检查mockData中的数据格式
- 确认form消息类型正确注册
- 查看控制台错误信息

### 2. 校验不生效
- 检查正则表达式格式
- 确认minLength/maxLength配置
- 验证required字段设置

### 3. Select选择无响应
- 确认Dialog组件正确导入
- 检查options数组格式
- 验证onPress回调函数

### 4. 样式异常
- 检查textareaInput样式应用
- 确认select按钮样式正确
- 验证错误状态样式

## 性能测试

### 大数据量测试
- 创建包含大量选项的select
- 测试长文本输入性能
- 验证复杂校验规则性能

### 内存泄漏测试
- 多次切换不同表单
- 验证组件正确销毁
- 检查事件监听器清理

## 测试完成标准

- [ ] 所有6个测试用例正常运行
- [ ] 各种校验规则正确触发
- [ ] 错误消息正确显示
- [ ] 表单提交成功
- [ ] 历史消息正确禁用
- [ ] 无JavaScript错误
- [ ] 样式显示正常
- [ ] 性能表现良好

## 上线前检查

1. **代码质量**：
   ```bash
   npm run lint    # 通过ESLint检查
   npm run build   # 构建成功
   ```

2. **功能完整性**：
   - 所有新功能正常工作
   - 向后兼容现有表单
   - 错误处理机制完善

3. **用户体验**：
   - 交互流畅自然
   - 错误提示清晰
   - 视觉样式统一

4. **文档更新**：
   - README.md更新
   - 接口文档同步
   - 使用示例完整