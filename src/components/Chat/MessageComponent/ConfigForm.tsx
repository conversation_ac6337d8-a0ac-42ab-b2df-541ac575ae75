import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    Switch,
} from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import React, { useContext, useEffect, useState } from 'react';

import { useSendMessage } from '../../../hooks/useSendMessage';
import { EntryPoint } from '../../../types';

import Condition from '@/components/Condition/Condition';
import { AnswerContext } from '@/components/MessageBox/Answer/AnswerContext';

interface ConfigFormProps {
    config: {
        label: string;
        labelStyle?: 'bold'; // 标签样式
        type: 'switch' | 'select' | 'timeRangePicker';
        options?: string[]; // select需要，switch和timeRangePicker不需要
        defaultValue?: string; // 默认值 'true' | 'false'，'13:00-18:00'
    }[];
    formId: string; // 前端不使用，触发提问时透传给后端
    buttonText?: string; // 按钮文案，默认为确定
    history?: boolean; // 是否是历史消息
}

// Switch组件
const SwitchField: React.FC<{
    label: string;
    labelStyle?: 'bold';
    value: boolean;
    onChange: (value: boolean) => void;
    disabled?: boolean;
}> = ({ label, labelStyle, value, onChange, disabled }) => {
    return (
        <View style={styles.formItem}>
            <Text style={[styles.label, labelStyle === 'bold' && styles.labelBold]}>
                {label}
            </Text>
            <Switch
                value={value}
                onValueChange={onChange}
                disabled={disabled}
                trackColor={{ false: '#E8E8E8', true: '#FFD100' }}
                thumbColor={value ? '#FFFFFF' : '#FFFFFF'}
                ios_backgroundColor="#E8E8E8"
            />
        </View>
    );
};

// Select组件
const SelectField: React.FC<{
    label: string;
    labelStyle?: 'bold';
    options: string[];
    value: string;
    onChange: (value: string) => void;
    disabled?: boolean;
}> = ({ label, labelStyle, options, value, onChange, disabled }) => {
    const [isOpen, setIsOpen] = useState(false);

    return (
        <View style={styles.formItem}>
            <Text style={[styles.label, labelStyle === 'bold' && styles.labelBold]}>
                {label}
            </Text>
            <TouchableOpacity
                style={[styles.selectButton, disabled && styles.selectButtonDisabled]}
                onPress={() => !disabled && setIsOpen(!isOpen)}
                disabled={disabled}
            >
                <Text style={[styles.selectText, disabled && styles.selectTextDisabled]}>
                    {value || '请选择'}
                </Text>
                <Icon
                    type="expand-more"
                    size={16}
                    tintColor={disabled ? '#999999' : '#666666'}
                />
            </TouchableOpacity>

            <Condition condition={[isOpen && !disabled]}>
                <View style={styles.optionsContainer}>
                    {options.map((option, index) => (
                        <TouchableOpacity
                            key={index}
                            style={[
                                styles.optionItem,
                                option === value && styles.optionItemSelected,
                                index === options.length - 1 && styles.optionItemLast
                            ]}
                            onPress={() => {
                                onChange(option);
                                setIsOpen(false);
                            }}
                        >
                            <Text style={[
                                styles.optionText,
                                option === value && styles.optionTextSelected
                            ]}>
                                {option}
                            </Text>
                        </TouchableOpacity>
                    ))}
                </View>
            </Condition>
        </View>
    );
};

// TimeRangePicker组件
const TimeRangePickerField: React.FC<{
    label: string;
    labelStyle?: 'bold';
    value: string;
    onChange: (value: string) => void;
    disabled?: boolean;
}> = ({ label, labelStyle, value, onChange, disabled }) => {
    return (
        <View style={styles.formItem}>
            <Text style={[styles.label, labelStyle === 'bold' && styles.labelBold]}>
                {label}
            </Text>
            <TouchableOpacity
                style={[styles.timeRangeButton, disabled && styles.timeRangeButtonDisabled]}
                disabled={disabled}
                onPress={() => {
                    // 这里可以集成时间选择器，暂时使用简单的实现
                    if (!disabled) {
                        // 简单的时间范围切换逻辑，实际项目中应该使用时间选择器
                        const timeRanges = ['09:00-18:00', '10:00-19:00', '13:00-18:00'];
                        const currentIndex = timeRanges.indexOf(value);
                        const nextIndex = (currentIndex + 1) % timeRanges.length;
                        onChange(timeRanges[nextIndex]);
                    }
                }}
            >
                <Text style={[styles.timeRangeText, disabled && styles.timeRangeTextDisabled]}>
                    {value || '请选择时间范围'}
                </Text>
                <Icon
                    type="expand-more"
                    size={16}
                    tintColor={disabled ? '#999999' : '#666666'}
                />
            </TouchableOpacity>
        </View>
    );
};

export const ConfigForm: React.FC<ConfigFormProps> = ({
    config,
    formId,
    buttonText = '保存',
    history: _history,
}) => {
    const [values, setValues] = useState<Record<string, string>>(() => {
        const initialValues: Record<string, string> = {};
        config.forEach((item) => {
            initialValues[item.label] = item.defaultValue || '';
        });
        return initialValues;
    });
    const [isSubmitted, setIsSubmitted] = useState(false);

    const { setWithForm } = useContext(AnswerContext);
    useEffect(() => {
        setWithForm(true);
    }, []);

    const { send } = useSendMessage();

    const handleSubmit = () => {
        setIsSubmitted(true);

        // 构造JSON格式的数据
        const formData = {
            formType: formId,
            ...values,
        };

        // 发送hideSpan类型的消息
        send(JSON.stringify(formData), undefined, EntryPoint.form_input);
    };

    const handleChange = (label: string, value: string) => {
        setValues(prev => ({
            ...prev,
            [label]: value,
        }));
    };

    return (
        <View style={styles.container}>
            <Text style={styles.title}>拜访签入提醒</Text>

            {config.map((item, index) => {
                const fieldValue = values[item.label];
                const isDisabled = isSubmitted;

                switch (item.type) {
                    case 'switch':
                        return (
                            <SwitchField
                                key={index}
                                label={item.label}
                                labelStyle={item.labelStyle}
                                value={fieldValue === 'true'}
                                onChange={(value) => handleChange(item.label, value.toString())}
                                disabled={isDisabled}
                            />
                        );
                    case 'select':
                        return (
                            <SelectField
                                key={index}
                                label={item.label}
                                labelStyle={item.labelStyle}
                                options={item.options || []}
                                value={fieldValue}
                                onChange={(value) => handleChange(item.label, value)}
                                disabled={isDisabled}
                            />
                        );
                    case 'timeRangePicker':
                        return (
                            <TimeRangePickerField
                                key={index}
                                label={item.label}
                                labelStyle={item.labelStyle}
                                value={fieldValue}
                                onChange={(value) => handleChange(item.label, value)}
                                disabled={isDisabled}
                            />
                        );
                    default:
                        return null;
                }
            })}

            <Condition condition={[!isSubmitted]}>
                <TouchableOpacity
                    style={styles.submitButton}
                    onPress={handleSubmit}
                >
                    <Text style={styles.submitButtonText}>{buttonText}</Text>
                </TouchableOpacity>
            </Condition>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        borderRadius: 8,
        padding: 16,
        backgroundColor: '#FFFFFF',
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#222222',
        marginBottom: 20,
    },
    formItem: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 20,
        minHeight: 44,
    },
    label: {
        fontSize: 16,
        color: '#222222',
        flex: 1,
    },
    labelBold: {
        fontWeight: 'bold',
    },
    selectButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: '#F5F6FA',
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 12,
        minWidth: 120,
        borderWidth: 1,
        borderColor: 'transparent',
    },
    selectButtonDisabled: {
        opacity: 0.5,
    },
    selectText: {
        fontSize: 16,
        color: '#222222',
        marginRight: 8,
    },
    selectTextDisabled: {
        color: '#999999',
    },
    optionsContainer: {
        position: 'absolute',
        top: '100%',
        right: 0,
        backgroundColor: '#FFFFFF',
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#E8E8E8',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
        zIndex: 1000,
        minWidth: 120,
    },
    optionItem: {
        paddingHorizontal: 12,
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
    },
    optionItemSelected: {
        backgroundColor: '#FFFBE0',
    },
    optionItemLast: {
        borderBottomWidth: 0,
    },
    optionText: {
        fontSize: 16,
        color: '#222222',
    },
    optionTextSelected: {
        color: '#222222',
        fontWeight: '500',
    },
    timeRangeButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: '#F5F6FA',
        borderRadius: 8,
        paddingHorizontal: 12,
        paddingVertical: 12,
        minWidth: 140,
        borderWidth: 1,
        borderColor: 'transparent',
    },
    timeRangeButtonDisabled: {
        opacity: 0.5,
    },
    timeRangeText: {
        fontSize: 16,
        color: '#222222',
        marginRight: 8,
    },
    timeRangeTextDisabled: {
        color: '#999999',
    },
    submitButton: {
        backgroundColor: '#6C5CE7',
        borderRadius: 24,
        paddingVertical: 16,
        alignItems: 'center',
        marginTop: 16,
        marginHorizontal: 0,
    },
    submitButtonText: {
        fontSize: 16,
        color: '#FFFFFF',
        fontWeight: '500',
    },
});
