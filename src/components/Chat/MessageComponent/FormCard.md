# FormCard 组件 PRD

## 1. 功能概述
FormCard 是一个通用的表单卡片组件，用于在对话界面中收集用户输入信息。支持多种输入形式：输入框、多行文本、单选按钮、下拉选择。

## 2. 交互设计

### 2.1 布局结构
- 表单项垂直排列
- 底部包含确定按钮
- label需要对齐

### 2.2 表单项类型
1. 输入框类型 (input)
   - 支持文本输入
   - 可设置默认值
   - 支持占位符文本
   - 输入框高度固定
   - 支持长度校验（minLength、maxLength）

2. 多行文本类型 (textarea)
   - 支持多行文本输入
   - 可设置行数
   - 支持长度校验
   - 文本从顶部开始输入

3. 单选类型 (radio)
   - 横向排列的单选按钮组
   - 选项文字位于按钮右侧
   - 支持默认选中值
   - 选项间距均匀

4. 下拉选择类型 (select)
   - 点击触发选择弹窗
   - 支持多选项列表
   - 显示当前选中值

### 2.3 确定按钮
- 位于表单底部
- 使用主题色背景
- 圆角设计
- 可自定义按钮文案

## 3. 视觉规范

### 3.1 卡片样式
- 不需要容器

### 3.2 表单项样式
- 标签文字：14px，#222222
- 输入框(input)：
  - 边框：1px solid #E8E8E8
  - 圆角：4px
  - 内边距：8px 12px
  - 文字：14px，#222222
  - 占位符：14px，#999999

- 单选按钮（radio）：
  - 按钮大小：16px
  - 选中色：主题色
  - 选项文字：14px，#222222
  - 选项间距：16px

### 3.3 确定按钮样式
- 高度：40px
- 文字：14px，#FFFFFF
- 背景色：美团黄
- 圆角：20px

## 4. 交互响应
- 表单提交时进行必要的数据验证（必填校验、长度校验、正则校验）
- 历史消息或者已提交的卡片按钮隐藏且选项置灰
- 仅最新回答的表单可填写&提交，其他均disable
- 支持hideSpan提交方式（隐藏提交按钮）

## 5. 接口定义

### Props 定义
```typescript
interface FormCardProps {
    config: {
        label: string;                    // 表单项标签
        type: 'radio' | 'input' | 'textarea' | 'select'; // 表单项类型
        options?: string[];               // radio和select需要的选项列表
        defaultValue?: string;            // 默认值
        tooltip?: string;                 // label后的问号提示词
        labelWrap?: boolean;              // 是否换行
        regExp?: string;                  // 正则校验规则
        message?: string;                 // 错误提示
        required?: boolean;               // 是否必填，默认true
        minLength?: number;               // 最小长度
        maxLength?: number;               // 最大长度
        multiline?: boolean;              // 是否多行（用于input类型）
        numberOfLines?: number;           // 行数（用于多行输入）
        placeholder?: string;             // 自定义占位符
    }[];
    buttonText?: string;                  // 确定按钮文案
    history?: boolean;                    // 是否是历史消息
    title?: string;                       // 标题
    subTitle?: string;                    // 副标题
    labelSpan?: number;                   // label占行宽的比例
    hideSpan?: boolean;                   // 隐藏提交按钮
    onSubmit?: (values: Record<string, string>) => void; // 自定义提交回调
}
```

## 6. 使用示例

```typescript
const config = [
    {
        label: 'BDmis',
        type: 'input',
        defaultValue: '',
        minLength: 3,
        maxLength: 20,
        placeholder: '请输入BDmis账号'
    },
    {
        label: '绩效目标',
        type: 'radio', // radio为单选按钮组，即色块组，而非传统意义上的radio
        options: ['100%', '120%', '150%'],
        defaultValue: '100%'
    },
    {
        label: '详细描述',
        type: 'textarea',
        numberOfLines: 4,
        maxLength: 500,
        placeholder: '请详细描述问题'
    },
    {
        label: '选择类型',
        type: 'select',
        options: ['类型A', '类型B', '类型C'],
        placeholder: '请选择类型'
    }
];

<FormCard
    config={config}
    buttonText="确定"
    title="表单标题"
    subTitle="这是一个示例表单"
    hideSpan={false}
    onSubmit={(values) => {
        console.log('提交的值:', values);
    }}
/>
```