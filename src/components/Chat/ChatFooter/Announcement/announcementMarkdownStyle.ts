import { Platform } from 'react-native';

// 公告专用的markdown样式，基于现有markdownStyles但适配深色背景
export const announcementMarkdownStyles = {
    // Headings - 调整为公告场景的紧凑样式
    heading1: {
        flexDirection: 'row',
        fontSize: 14,
        marginVertical: 2,
        fontWeight: 'bold',
        color: '#FFF',
    },
    heading2: {
        flexDirection: 'row',
        fontSize: 13,
        marginVertical: 2,
        fontWeight: 'bold',
        color: '#FFF',
    },
    heading3: {
        flexDirection: 'row',
        fontSize: 12,
        marginVertical: 2,
        fontWeight: 'bold',
        color: '#FFF',
    },
    heading4: {
        flexDirection: 'row',
        fontSize: 12,
        marginVertical: 2,
        fontWeight: 'bold',
        color: '#FFF',
    },
    heading5: {
        flexDirection: 'row',
        fontSize: 11,
        marginVertical: 2,
        fontWeight: 'bold',
        color: '#FFF',
    },
    heading6: {
        flexDirection: 'row',
        fontSize: 11,
        marginVertical: 2,
        fontWeight: 'bold',
        color: '#FFF',
    },

    // Horizontal Rule
    hr: {
        marginVertical: 4,
        backgroundColor: '#999',
        height: 1,
        width: '100%',
    },

    // Emphasis
    strong: {
        fontWeight: 'bold',
        color: '#FFF',
    },
    em: {
        fontStyle: 'italic',
        color: '#FFF',
    },
    s: {
        textDecorationLine: 'line-through',
        color: '#FFF',
    },

    // Blockquotes - 适配深色背景
    blockquote: {
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
        borderColor: '#999',
        borderLeftWidth: 2,
        marginLeft: 2,
        paddingHorizontal: 4,
        paddingVertical: 2,
    },

    // Lists - 紧凑样式
    bullet_list: {
        lineHeight: 16,
    },
    ordered_list: {
        lineHeight: 16,
    },
    list_item: {
        flexDirection: 'row',
        justifyContent: 'flex-start',
        marginVertical: 1,
    },
    bullet_list_icon: {
        marginLeft: 2,
        marginRight: 2,
        color: '#FFF',
    },
    bullet_list_content: {
        flex: 1,
        color: '#FFF',
    },
    ordered_list_icon: {
        marginLeft: 4,
        marginRight: 4,
        color: '#FFF',
    },
    ordered_list_content: {
        flex: 1,
        color: '#FFF',
    },

    // Code - 适配深色背景
    code_inline: {
        borderWidth: 1,
        borderColor: '#999',
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
        paddingHorizontal: 4,
        paddingVertical: 2,
        borderRadius: 2,
        fontSize: 11,
        color: '#FFF',
        ...Platform.select({
            ['ios']: {
                fontFamily: 'Courier',
            },
            ['android']: {
                fontFamily: 'monospace',
            },
        }),
    },
    code_block: {
        marginVertical: 4,
        borderWidth: 1,
        borderColor: '#999',
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
        padding: 6,
        borderRadius: 4,
        fontSize: 11,
        color: '#FFF',
        ...Platform.select({
            ['ios']: {
                fontFamily: 'Courier',
            },
            ['android']: {
                fontFamily: 'monospace',
            },
        }),
    },
    fence: {
        borderWidth: 1,
        borderColor: '#999',
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
        padding: 6,
        borderRadius: 4,
        fontSize: 11,
        color: '#FFF',
        ...Platform.select({
            ['ios']: {
                fontFamily: 'Courier',
            },
            ['android']: {
                fontFamily: 'monospace',
            },
        }),
    },

    // Links - 使用系统统一的链接颜色
    link: {
        fontSize: 12,
        textDecorationLine: undefined,
        color: '#FF6A00', // 与系统其他链接颜色保持一致
        backgroundColor: 'transparent',
    },
    blocklink: {
        flex: 1,
        borderColor: '#FF6A00',
        borderBottomWidth: 1,
    },

    // Images - 公告中一般不使用图片，但保留兼容性
    image: {
        flex: 1,
    },

    // Text Output - 白色文字
    text: {
        color: '#FFF',
        fontSize: 12,
        lineHeight: 18,
    },
    textgroup: {
        color: '#FFF',
    },
    paragraph: {
        marginTop: 2,
        marginBottom: 2,
        flexWrap: 'wrap',
        flexDirection: 'row',
        alignItems: 'flex-start',
        justifyContent: 'flex-start',
        width: '100%',
        lineHeight: 18,
    },
    hardbreak: {
        width: '100%',
        height: 1,
    },
    softbreak: {},

    // Tables - 简化的表格样式，适配公告场景
    table: {
        borderWidth: 1,
        lineHeight: 16,
        borderColor: '#999',
        borderRadius: 4,
        marginVertical: 4,
    },
    thead: {
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
        borderTopLeftRadius: 4,
        borderTopRightRadius: 4,
    },
    tbody: {},
    th: {
        flex: 1,
        padding: 4,
        borderRightWidth: 1,
        borderColor: '#999',
        fontWeight: 'bold',
        color: '#FFF',
        fontSize: 11,
    },
    tr: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderColor: '#999',
    },
    td: {
        flex: 1,
        padding: 4,
        borderRightWidth: 1,
        borderColor: '#999',
        color: '#FFF',
        fontSize: 11,
    },

    // Unused but retained for completeness
    pre: {},
    inline: {},
    span: {},
};
