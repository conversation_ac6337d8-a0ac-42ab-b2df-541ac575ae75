import { View, Text, StyleSheet, TouchableOpacity } from '@mrn/react-native';
import React, { useState, useEffect, useRef, useCallback } from 'react';

import CardWithAvatar from '../CardWithAvatar';

import { useSendMessage } from '@/hooks/useSendMessage';
import { useUiState } from '@/store/uiState';
import { EntryPointType } from '@/types';

interface PoiContent {
    label?: string;
    value: string;
    block?: boolean;
}

interface Poi {
    poiId: string;
    avatar: string;
    title: string;
    content: PoiContent[];
    isSignedIn: boolean;
    signInTime?: string;
    checkInTimestamp?: number; // 签入时间戳，用于计算倒计时
}

interface ToSignPoiListProps {
    title?: string;
    reminderButtonText?: string;
    reminderButtonQuestion?: string;
    moreActionButtonText?: string;
    poiList: Poi[];
    onSignIn?: (poiId: string) => void;
    onReminderPress?: () => void;
    timeoutMinutes?: number; // 超时时间（分钟）
}

const ToSignPoiList: React.FC<ToSignPoiListProps> = ({
    title = '请选择你想签到的商家',
    reminderButtonText = '签入提醒设置',
    reminderButtonQuestion = '设置签入提醒',
    moreActionButtonText = '查看更多商家',
    poiList,
    onSignIn,
    onReminderPress,
    timeoutMinutes = 60, // 默认60分钟
}) => {
    const { send } = useSendMessage();
    const [countdowns, setCountdowns] = useState<Record<string, string>>({});
    const [hasSignedInPoi, setHasSignedInPoi] = useState<boolean>(false);
    const { setPoiSelectorOpen, setPanelOpen } = useUiState();
    const timerRef = useRef<NodeJS.Timeout | null>(null);

    // 检查是否有已签入的商家
    useEffect(() => {
        const signedInPoi = poiList.find((poi) => poi.isSignedIn);
        setHasSignedInPoi(!!signedInPoi);
    }, [poiList]);

    // 计算倒计时
    const calculateCountdown = useCallback(() => {
        const now = Date.now();
        const newCountdowns: Record<string, string> = {};

        poiList.forEach((poi) => {
            if (poi.isSignedIn && poi.checkInTimestamp) {
                const elapsedTime = now - poi.checkInTimestamp; // 已经过去的时间（毫秒）
                const timeoutTime = timeoutMinutes * 60 * 1000; // 超时时间（毫秒）
                const remaining = timeoutTime - elapsedTime; // 剩余时间（毫秒）

                if (remaining <= 0) {
                    newCountdowns[poi.poiId] = '已超时';
                } else {
                    const minutes = Math.floor(remaining / (60 * 1000));
                    const seconds = Math.floor(
                        (remaining % (60 * 1000)) / 1000,
                    );
                    newCountdowns[poi.poiId] = `${minutes
                        .toString()
                        .padStart(2, '0')}:${seconds
                        .toString()
                        .padStart(2, '0')}`;
                }
            } else if (poi.isSignedIn && poi.signInTime) {
                // 如果没有时间戳但有签入时间字符串，直接显示
                newCountdowns[poi.poiId] = poi.signInTime;
            }
        });

        setCountdowns(newCountdowns);
    }, [poiList, timeoutMinutes]);

    // 设置倒计时定时器
    useEffect(() => {
        // 初始计算一次
        calculateCountdown();

        // 设置定时器，每秒更新一次
        timerRef.current = setInterval(calculateCountdown, 1000);

        // 清理函数
        return () => {
            if (timerRef.current) {
                clearInterval(timerRef.current);
                timerRef.current = null;
            }
        };
    }, [calculateCountdown]);

    const handleSignIn = (poiId: string) => {
        if (hasSignedInPoi) {
            return;
        } // 如果已有签入商家，禁止操作

        if (onSignIn) {
            onSignIn(poiId);
        } else {
            // 默认签入逻辑
            console.log('签入商家:', poiId);
            // 发送签入成功消息
            const poi = poiList.find((p) => p.poiId === poiId);
            if (poi) {
                send(
                    `【${poi.title}】签入成功～放心，我也会提醒你签出。`,
                    EntryPointType.SECTION,
                );
            }
        }
    };

    const handleShowMore = () => {
        setPoiSelectorOpen(true);
        setPanelOpen(false);
    };

    const handleReminderPress = () => {
        if (onReminderPress) {
            onReminderPress();
        } else {
            // 默认签入提醒设置逻辑
            send(reminderButtonQuestion, EntryPointType.USER);
        }
    };

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <Text style={styles.title}>{title}</Text>
                <TouchableOpacity
                    style={styles.reminderButton}
                    onPress={handleReminderPress}
                >
                    <Text style={styles.reminderButtonText}>
                        {reminderButtonText}
                    </Text>
                </TouchableOpacity>
            </View>

            <View style={styles.poiListContainer}>
                {poiList.map((poi, index) => (
                    <View
                        key={poi.poiId}
                        style={[
                            styles.poiItem,
                            index === poiList.length - 1
                                ? {
                                      borderBottomWidth: 0,
                                      marginBottom: 2,
                                      paddingBottom: 2,
                                  }
                                : {},
                        ]}
                    >
                        <CardWithAvatar
                            avatar={poi.avatar}
                            title={poi.title}
                            content={poi.content}
                            actionButton={
                                poi.isSignedIn ? (
                                    <View style={styles.signedInButton}>
                                        <Text
                                            style={[
                                                styles.signedInText,
                                                countdowns[poi.poiId] ===
                                                    '已超时' &&
                                                    styles.timeoutText,
                                            ]}
                                        >
                                            已签入{' '}
                                            {countdowns[poi.poiId] ||
                                                poi.signInTime}
                                        </Text>
                                    </View>
                                ) : (
                                    <TouchableOpacity
                                        style={[
                                            styles.signInButton,
                                            hasSignedInPoi &&
                                                styles.disabledButton,
                                        ]}
                                        onPress={() => handleSignIn(poi.poiId)}
                                        disabled={hasSignedInPoi}
                                    >
                                        <Text
                                            style={[
                                                styles.signInText,
                                                hasSignedInPoi &&
                                                    styles.disabledText,
                                            ]}
                                        >
                                            签入
                                        </Text>
                                    </TouchableOpacity>
                                )
                            }
                        />
                    </View>
                ))}
            </View>

            <TouchableOpacity
                style={styles.moreButton}
                onPress={handleShowMore}
            >
                <Text style={styles.moreButtonText}>
                    {moreActionButtonText} {'>'}
                </Text>
            </TouchableOpacity>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        padding: 0,
        marginBottom: 0,
        elevation: 2,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16,
    },
    title: {
        fontSize: 18,
        fontWeight: '600',
        color: '#222',
    },
    reminderButton: {
        backgroundColor: '#EFEDFF',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 16,
    },
    reminderButtonText: {
        fontSize: 14,
        color: '#4322FF',
        fontWeight: '400',
    },
    poiListContainer: {
        marginBottom: 8,
    },
    poiItem: {
        marginBottom: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#F5F5F5',
        paddingBottom: 12,
    },
    signInButton: {
        backgroundColor: '#4021FF',
        paddingHorizontal: 14,
        paddingVertical: 9,
        borderRadius: 20,
    },
    disabledButton: {
        backgroundColor: '#EEE',
    },
    signInText: {
        fontSize: 14,
        color: '#FFF',
        fontWeight: '500',
    },
    disabledText: {
        color: '#ACACAC',
    },
    signedInButton: {
        backgroundColor: '#EFEDFF',
        paddingHorizontal: 16,
        paddingVertical: 10,
        borderRadius: 20,
    },
    signedInText: {
        fontSize: 14,
        color: '#4F44E0',
        fontWeight: '500',
    },
    timeoutText: {
        color: '#FF6B6B',
    },
    moreButton: {
        paddingBottom: 2,
    },
    moreButtonText: {
        fontSize: 16,
        fontWeight: '500',
        color: '#222',
    },
});

export default ToSignPoiList;
