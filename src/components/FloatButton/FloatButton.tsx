import {
    Animated,
    LayoutAnimation,
    PanResponder,
    Platform,
    StyleSheet,
    TouchableOpacity,
    View,
    Dimensions,
} from '@mrn/react-native';
import { useDebounceFn, useGetState } from 'ahooks';
import React, { useEffect, useRef } from 'react';

import { startAnimation } from '../../utils/animation';
import AssistantIcon from '../AssistantIcon';

const { width: screenWidth, height: screenHeight } = Dimensions.get('screen');

interface FloatButton {
    scrollY?: Animated.Value;
    minMarginTop?: number;
    minMarginBottom?: number;
    onPress?: () => void;
}

const styles = StyleSheet.create({
    container: {
        position: 'absolute',
    },
    icon: {
        ...Platform.select({
            ios: {
                shadowColor: '#171717',
                shadowOffset: {
                    width: 0,
                    height: 2,
                },
                shadowOpacity: 0.4,
                shadowRadius: 8,
            },
            android: {
                elevation: 5,
                zIndex: 5,
                backgroundColor: '#fff',
                borderRadius: 55,
            },
        }),
    },
});

const FloatButton = (props: FloatButton) => {
    const minMarginTop = props.minMarginTop || 100;
    const minMarginBottom = props.minMarginBottom || minMarginTop;
    const onPress = props.onPress || (() => {});

    const pan = useRef(new Animated.ValueXY()).current;
    const opacity = useRef(new Animated.Value(1)).current;
    const timer = useRef(null);
    const animationPlaying = useRef(false);

    // const [moving, setMoving] = useState(false);
    const handlePress = useDebounceFn(
        () => {
            onPress();
        },
        { wait: 200 },
    );

    const foldIcon = () => {
        // animation playing, 不必再次执行，避免打断动画
        if (animationPlaying.current) {
            return;
        }
        animationPlaying.current = true;
        const position = getPosition();
        Animated.spring(
            pan, // Auto-multiplexed
            {
                toValue: { x: 55 * (position.right ? 1 : -1), y: 0 },
                useNativeDriver: true,
            },
        ).start(() => (animationPlaying.current = false));
        Animated.spring(opacity, {
            toValue: 0.5,
            useNativeDriver: true,
        }).start();
    };

    const unfoldIcon = () => {
        Animated.spring(
            pan, // Auto-multiplexed
            { toValue: { x: 0, y: 0 }, useNativeDriver: true }, // Back to zero
        ).start();
        Animated.spring(opacity, {
            toValue: 1,
            useNativeDriver: true,
        }).start();
    };

    useEffect(() => {
        if (!props.scrollY) {
            return;
        }

        props.scrollY.addListener(() => {
            foldIcon();

            clearTimeout(timer.current);
            // 滚动停止
            timer.current = setTimeout(() => {
                unfoldIcon();
            }, 2000);
        });

        return () => {
            clearTimeout(timer.current);
            props.scrollY.removeAllListeners();
        };
    }, [props.scrollY]);

    const [position, setPosition, getPosition] = useGetState<
        Partial<{
            top: number;
            left: number;
            right: number;
            bottom: number;
        }>
    >({ right: 20, bottom: 20 });
    const panResponder = useRef(
        PanResponder.create({
            onStartShouldSetPanResponder: () => {
                return true;
            },
            onMoveShouldSetPanResponder: () => {
                // setMoving(true);
                return true;
            },
            onPanResponderMove: (_, gestureState) => {
                const { dx, dy } = gestureState;
                if (Math.abs(dx) < 10 && Math.abs(dy) < 10) {
                    return;
                }
                setPosition({
                    top: gestureState.moveY,
                    left: gestureState.moveX,
                });
                startAnimation();
            },
            onPanResponderRelease: (_, gestureState) => {
                // setMoving(false);
                const position = getPosition();
                const { dx, dy } = gestureState;
                if (Math.abs(dx) < 10 && Math.abs(dy) < 10) {
                    // 如果移动距离很小，则认为是点击而非拖动
                    return handlePress.run();
                }

                const draftPosition = { ...position };
                if (gestureState.moveX > screenWidth / 2) {
                    draftPosition.right = 20;
                    draftPosition.left = undefined;
                } else {
                    draftPosition.left = 20;
                    draftPosition.right = undefined;
                }
                draftPosition.top = gestureState.moveY;
                if (gestureState.moveY > screenHeight - minMarginBottom) {
                    draftPosition.bottom = minMarginBottom;
                    draftPosition.top = undefined;
                }
                if (gestureState.moveY < minMarginTop) {
                    draftPosition.top = minMarginTop;
                    draftPosition.bottom = undefined;
                }
                setPosition(draftPosition);
                startAnimation(LayoutAnimation.Presets.spring);
            },
        }),
    ).current;

    return (
        // 拖动动画效果容器
        <View
            style={[styles.container, position]}
            {...panResponder.panHandlers}
        >
            {/* 折叠动画效果容器 */}
            {
                <Animated.View
                    style={{
                        transform: [
                            { translateX: pan.x },
                            { translateY: pan.y },
                        ],
                        opacity: opacity,
                    }}
                >
                    <TouchableOpacity
                        style={styles.icon}
                        onPress={handlePress.run}
                    >
                        <AssistantIcon />
                    </TouchableOpacity>
                </Animated.View>
            }
        </View>
    );
};

export default FloatButton;
