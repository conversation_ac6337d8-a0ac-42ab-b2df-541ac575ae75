import { useCallback, useEffect, useRef, useState } from 'react';
import { DeviceEventEmitter } from 'react-native';

import useMessage from '../../../hooks/useMessage';
import { SOURCE } from '../../../types';
import { EntryPointType } from '../../../types';
import { trackEvent, TrackEventType } from '../../../utils/track';

// 回调函数接口
export interface PoiSignInCallbacks {
    onPoiSignIn?: (poiId: number) => void;
    onRefreshLocation?: () => void;
    onMorePress?: () => Promise<void>;
}

// 回调函数接口
export interface PoiSignOutCallbacks {
    onSignOut?: (url: string) => void;
}

// 商家信息接口
export interface PoiInfo {
    poiId: number;
    name: string;
    avatar?: string;
    url: string;
    checkInTime?: number; // 签入时间，毫秒时间戳
}

// 事件名称常量
export const EVENTS = {
    SHOW_POI_SIGN_IN_BUBBLE: 'SHOW_POI_SIGN_IN_BUBBLE',
    SHOW_POI_SIGN_OUT_BUBBLE: 'SHOW_POI_SIGN_OUT_BUBBLE',
};

// 签入状态枚举
export enum PoiSignStatus {
    NONE = 'NONE', // 无签入状态
    SIGNING_IN = 'SIGNING_IN', // 签入中（显示可签入商家列表）
    SIGNED_IN = 'SIGNED_IN', // 已签入（显示签出提醒）
}

// 商家信息接口
export interface PoiItem {
    poiId: number;
    avatar?: string; // 商家头像
    name: string; // 商家名称
    distance?: string; // 距离，如 "100m"
    isCheckIn?: boolean; // 是否已签入
    checkInTime?: number; // 签入时间，毫秒时间戳
    url?: string; // 签出链接
}

// 回调函数接口
export interface PoiCallbacks {
    onPoiSignIn?: (poiId: number) => void;
    onRefreshLocation?: () => void;
    onMorePress?: () => Promise<void>;
    onSignOut?: (url: string) => void;
}

/**
 * 统一管理签入和签出状态的hook
 * @param visible 是否可见
 * @param source 来源
 * @param autoHideDelay 自动隐藏延迟时间
 * @param onShowBubble 显示气泡时的回调
 * @param onHideBubble 隐藏气泡时的回调
 * @param isFolded 是否处于折叠状态
 * @param unfoldIcon 解除折叠的函数
 */
const usePoiStatus = (
    visible: boolean,
    source: SOURCE,
    autoHideDelay: number = 5000,
    onShowBubble?: () => void,
    onHideBubble?: () => void,
    isFolded?: boolean,
    unfoldIcon?: () => void,
) => {
    // 签入状态
    const [signStatus, setSignStatus] = useState<PoiSignStatus>(
        PoiSignStatus.NONE,
    );

    // 签入商家列表（用于签入中状态）
    const [poiList, setPoiList] = useState<PoiItem[]>([]);

    // 已签入商家信息（用于已签入状态）
    const [signedPoiInfo, setSignedPoiInfo] = useState<PoiItem | null>(null);

    // 气泡自动隐藏定时器
    const bubbleTimer = useRef<NodeJS.Timeout | null>(null);

    // 回调函数
    const [callbacks, setCallbacks] = useState<PoiCallbacks>({});

    /**
     * 隐藏所有气泡
     */
    const hideAllBubbles = useCallback(() => {
        setSignStatus(PoiSignStatus.NONE);
        setPoiList([]);
        setSignedPoiInfo(null);

        // 通知父组件气泡隐藏
        onHideBubble?.();

        if (bubbleTimer.current) {
            clearTimeout(bubbleTimer.current);
            bubbleTimer.current = null;
        }
    }, [onHideBubble]);

    /**
     * 显示签入商家气泡
     */
    const showPoiSignInBubble = useCallback(
        (newPoiList: PoiItem[], newCallbacks?: PoiCallbacks) => {
            if (!visible) {
                return;
            }

            // 如果图标处于折叠状态，先解除折叠
            if (isFolded && unfoldIcon) {
                unfoldIcon();
            }

            // 隐藏其他气泡，只显示签入气泡
            setSignStatus(PoiSignStatus.SIGNING_IN);
            setPoiList(newPoiList);
            setSignedPoiInfo(null);
            setCallbacks({ ...newCallbacks });

            // 通知父组件气泡显示
            onShowBubble?.();

            // 设置自动隐藏定时器
            if (bubbleTimer.current) {
                clearTimeout(bubbleTimer.current);
            }
            bubbleTimer.current = setTimeout(() => {
                hideAllBubbles();
            }, autoHideDelay);
        },
        [
            visible,
            autoHideDelay,
            onShowBubble,
            hideAllBubbles,
            isFolded,
            unfoldIcon,
        ],
    );

    /**
     * 显示签出商家气泡
     */
    const showPoiSignOutBubble = useCallback(
        (poiInfo: PoiItem, newCallbacks?: PoiCallbacks) => {
            if (!visible) {
                return;
            }

            // 如果图标处于折叠状态，先解除折叠
            if (isFolded && unfoldIcon) {
                unfoldIcon();
            }

            // 隐藏其他气泡，只显示签出气泡
            setSignStatus(PoiSignStatus.SIGNED_IN);
            setPoiList([]);
            setSignedPoiInfo(poiInfo);
            setCallbacks({ ...newCallbacks });

            // 通知父组件气泡显示
            onShowBubble?.();

            // 设置自动隐藏定时器
            if (bubbleTimer.current) {
                clearTimeout(bubbleTimer.current);
            }
            bubbleTimer.current = setTimeout(() => {
                hideAllBubbles();
            }, autoHideDelay);
        },
        [
            visible,
            autoHideDelay,
            onShowBubble,
            hideAllBubbles,
            isFolded,
            unfoldIcon,
        ],
    );

    /**
     * 点击签入商家气泡中的签入按钮
     */
    const handlePoiSignIn = useCallback(
        (poiId: number) => {
            trackEvent(
                'assistant_poi_sign_in_click',
                { poiId },
                TrackEventType.MC,
            );
            // 调用外部传入的回调函数
            if (callbacks.onPoiSignIn) {
                callbacks.onPoiSignIn(poiId);
            }
            hideAllBubbles();
        },
        [callbacks, hideAllBubbles],
    );

    /**
     * 点击签入商家气泡中的刷新定位按钮
     */
    const handleRefreshLocation = useCallback(() => {
        trackEvent('assistant_refresh_location_click', {}, TrackEventType.MC);
        // 调用外部传入的回调函数
        if (callbacks.onRefreshLocation) {
            callbacks.onRefreshLocation();
        }
    }, [callbacks]);

    /**
     * 点击签入商家气泡中的更多商家按钮
     */
    const handleMorePress = useCallback(async () => {
        trackEvent('assistant_more_pois_click', {}, TrackEventType.MC);
        // 调用外部传入的回调函数
        if (callbacks.onMorePress) {
            await callbacks.onMorePress();
        } else {
            // 默认行为：使用useMessage发送消息
            const messageState = useMessage.getState();
            // messageState?.setShowHome(false);
            messageState.send(
                '附近有哪些可签入商家？',
                undefined, // bizId，这里不需要指定
                EntryPointType.USER, // 用户输入类型
                'visit_check_in_list', // entryPoint
            );
        }
        hideAllBubbles();
    }, [callbacks, hideAllBubbles]);

    /**
     * 点击签出按钮
     */
    const handleSignOut = useCallback(
        (url: string) => {
            trackEvent('assistant_poi_sign_out_click', {}, TrackEventType.MC);
            // 调用外部传入的回调函数
            if (callbacks.onSignOut) {
                callbacks.onSignOut(url);
            }
            hideAllBubbles();
        },
        [callbacks, hideAllBubbles],
    );

    // 监听显示签入商家气泡的事件
    useEffect(() => {
        const signInListener = DeviceEventEmitter.addListener(
            EVENTS.SHOW_POI_SIGN_IN_BUBBLE,
            (data) => {
                if (data && data.poiList) {
                    showPoiSignInBubble(data.poiList, data.callbacks);
                }
            },
        );

        const signOutListener = DeviceEventEmitter.addListener(
            EVENTS.SHOW_POI_SIGN_OUT_BUBBLE,
            (data) => {
                if (data && data.poiInfo) {
                    showPoiSignOutBubble(data.poiInfo, data.callbacks);
                }
            },
        );

        return () => {
            signInListener.remove();
            signOutListener.remove();
            if (bubbleTimer.current) {
                clearTimeout(bubbleTimer.current);
            }
        };
    }, [showPoiSignInBubble, showPoiSignOutBubble]);

    return {
        signStatus,
        poiList,
        signedPoiInfo,
        hideAllBubbles,
        showPoiSignInBubble,
        showPoiSignOutBubble,
        handlePoiSignIn,
        handleRefreshLocation,
        handleMorePress,
        handleSignOut,
    };
};

export default usePoiStatus;
