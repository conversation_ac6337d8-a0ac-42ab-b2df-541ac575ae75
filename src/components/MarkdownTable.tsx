import { useNavigation } from '@mfe/bee-foundation-navigation';
import { msi } from '@mfe/waimai-mfe-bee-common';
import {
    View,
    StyleSheet,
    TouchableOpacity,
    Text,
    ScrollView,
} from '@mrn/react-native';
import { Icon, Toast } from '@roo/roo-rn';
import React, { useMemo, useState } from 'react';

import Condition from './Condition/Condition';
import { MemoizedMarkdownInner } from './MessageBox/Answer/AnswerContent/MemoizedMarkdownInner';
import RNImage from './RNImage';
import NetImages from '../assets/images/homeRefactor';

interface MarkdownTableProps {
    markdownContent: string;
    containerWidth?: number;
    showRotateButton?: boolean;
    tableTitle?: string;
    onLinkPress?: (url: string) => void;
    showExpandButton?: boolean;
}

const MarkdownTable: React.FC<MarkdownTableProps> = ({
    markdownContent,
    containerWidth,
    showRotateButton,
    tableTitle,
    onLinkPress,
    showExpandButton,
}) => {
    const { header, rows } = React.useMemo(() => {
        const lines = markdownContent
            .trim()
            .split('\n')
            .filter((line) => line.includes('|'));
        if (lines.length < 2) {
            return { header: [], rows: [] };
        }

        const headerData = lines[0]
            .split('|')
            .slice(1, -1)
            .map((cell) => cell.trim());

        const rowsData = lines.slice(2).map((line) =>
            line
                .split('|')
                .slice(1, -1)
                .map((cell) => cell.trim()),
        );

        return { header: headerData, rows: rowsData };
    }, [markdownContent]);

    const [isExpanded, setIsExpanded] = useState(
        rows.length <= 10 || !showExpandButton,
    );

    const columnWidths = React.useMemo(() => {
        if (header.length === 0) {
            return [];
        }

        const PADDING_HORIZONTAL = 12;

        const getDisplayText = (text: string): string => {
            const linkMatch = text.match(/\[([^\]]+)\]\([^)]+\)/);
            if (linkMatch && linkMatch[1]) {
                return linkMatch[1];
            }
            return text;
        };

        const widths = header.map((h, i) => {
            const getWidth = (text: string, hasCjk: boolean) => {
                return (text.length / 2) * (hasCjk ? 15 : 8);
            };

            const calculateCellMaxWidth = (text: string) => {
                const lines = text.split(/<br\s*\/?>|\n/);
                let maxWidthForCell = 0;
                for (const line of lines) {
                    const hasCjk = /[\u4e00-\u9fa5]/.test(line);
                    const width = getWidth(line, hasCjk);
                    if (width > maxWidthForCell) {
                        maxWidthForCell = width;
                    }
                }
                return maxWidthForCell;
            };

            const headerText = getDisplayText(h || '');
            let maxWidth = (headerText.length / 2) * 15;

            for (const row of rows) {
                const cellText = getDisplayText(row[i] || '');
                const width = calculateCellMaxWidth(cellText);
                if (width > maxWidth) {
                    maxWidth = width;
                }
            }

            return (
                Math.max(100, Math.min(maxWidth, 200)) +
                PADDING_HORIZONTAL * 2 +
                16
            );
        });
        return widths;
    }, [header, rows]);

    const [sortConfig, setSortConfig] = React.useState<{
        key: number;
        direction: 'ascending' | 'descending';
    } | null>(null);

    const sortedRows = React.useMemo(() => {
        if (!sortConfig) {
            return rows;
        }
        const newRows = [...rows];
        newRows.sort((a, b) => {
            const aVal = a[sortConfig.key];
            const bVal = b[sortConfig.key];

            const comparison = aVal.localeCompare(bVal, undefined, {
                numeric: true,
                sensitivity: 'base',
            });

            return sortConfig.direction === 'ascending'
                ? comparison
                : -comparison;
        });
        return newRows;
    }, [rows, sortConfig]);

    const MAX_ROWS = 6;
    const displayRows = useMemo(() => {
        if (isExpanded) {
            return sortedRows;
        }
        return sortedRows.slice(0, MAX_ROWS);
    }, [isExpanded, sortedRows]);

    const requestSort = (key: number) => {
        let direction: 'ascending' | 'descending' = 'ascending';
        if (
            sortConfig &&
            sortConfig.key === key &&
            sortConfig.direction === 'ascending'
        ) {
            direction = 'descending';
        }
        setSortConfig({ key, direction });
    };
    const { navigate } = useNavigation<any>();

    if (header.length === 0) {
        return null;
    }

    return (
        <View>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View
                    style={[
                        styles.table,
                        containerWidth ? { width: containerWidth } : {},
                    ]}
                >
                    <View style={[styles.row, styles.headerRow]}>
                        {header.map((cell, cellIndex) => (
                            <TouchableOpacity
                                key={cellIndex}
                                style={[
                                    styles.cell,
                                    { width: columnWidths[cellIndex] },
                                    cellIndex === header.length - 1 && {
                                        borderRightWidth: 0,
                                    },
                                ]}
                                onPress={() => requestSort(cellIndex)}
                            >
                                <View style={styles.headerContent}>
                                    <Text style={styles.headerText}>
                                        {cell}
                                    </Text>
                                    {sortConfig?.key === cellIndex ? (
                                        <RNImage
                                            style={styles.sortIndicator}
                                            width={12}
                                            source={
                                                sortConfig.direction ===
                                                'ascending'
                                                    ? NetImages.sortUp
                                                    : NetImages.sortDown
                                            }
                                        />
                                    ) : (
                                        <RNImage
                                            width={12}
                                            style={styles.sortIndicator}
                                            source={{
                                                uri: NetImages.sortNormal,
                                            }}
                                        />
                                    )}
                                </View>
                            </TouchableOpacity>
                        ))}
                    </View>
                    {displayRows.map((row, rowIndex) => (
                        <View
                            key={rowIndex}
                            style={[
                                styles.row,
                                rowIndex === displayRows.length - 1 && {
                                    borderBottomWidth: 0,
                                },
                            ]}
                        >
                            {row.map((cell, cellIndex) => (
                                <TouchableOpacity
                                    onLongPress={() => {
                                        msi.setClipboardData({
                                            data: cell, // 需要复制的数据
                                            _mt: {
                                                sceneToken:
                                                    'bee-assistant-main', // 设置自己的隐私合规token
                                            },
                                            success: () => {
                                                Toast.open('复制成功', {
                                                    duration: Toast.SHORT,
                                                });
                                            },
                                            fail: () => {
                                                Toast.open('复制内容为空', {
                                                    duration: Toast.SHORT,
                                                });
                                            },
                                        });
                                    }}
                                    key={cellIndex}
                                    style={[
                                        styles.cell,
                                        { width: columnWidths[cellIndex] },
                                        cellIndex === row.length - 1 && {
                                            borderRightWidth: 0,
                                        },
                                    ]}
                                >
                                    <MemoizedMarkdownInner
                                        onLinkPress={onLinkPress}
                                    >
                                        {cell}
                                    </MemoizedMarkdownInner>
                                </TouchableOpacity>
                            ))}
                        </View>
                    ))}
                </View>
            </ScrollView>
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginBottom: 8,
                }}
            >
                <View style={{ flex: 1 }}>
                    <Condition condition={[showRotateButton]}>
                        <TouchableOpacity
                            style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'flex-start',
                                paddingHorizontal: 8,
                            }}
                            onPress={() =>
                                navigate('HorizontalTable', {
                                    mdData: markdownContent,
                                    title: tableTitle,
                                })
                            }
                            hitSlop={{
                                top: 10,
                                bottom: 10,
                                left: 10,
                                right: 10,
                            }}
                        >
                            <Icon type="rotate" size={14} tintColor="#666" />
                            <Text
                                style={{
                                    color: '#666',
                                    fontSize: 12,
                                    marginLeft: 4,
                                }}
                            >
                                横屏查看
                            </Text>
                        </TouchableOpacity>
                    </Condition>
                </View>
                <View style={{ flex: 1 }}>
                    <Condition
                        condition={[
                            rows.length <= MAX_ROWS || !showExpandButton,
                            true,
                        ]}
                    >
                        {null}
                        <TouchableOpacity
                            style={styles.expandButton}
                            onPress={() => setIsExpanded(!isExpanded)}
                            hitSlop={{
                                top: 10,
                                bottom: 10,
                                left: 10,
                                right: 10,
                            }}
                        >
                            <Text style={styles.expandButtonText}>
                                {isExpanded
                                    ? '收起'
                                    : `共${rows.length}  查看全部`}
                            </Text>
                            <Icon
                                type={
                                    isExpanded ? 'expand-less' : 'expand-more'
                                }
                                size={16}
                                tintColor="#666"
                            />
                        </TouchableOpacity>
                    </Condition>
                </View>

                <View style={{ flex: 1 }} />
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    table: {
        borderWidth: StyleSheet.hairlineWidth,
        borderColor: '#DFE3E6',
        borderRadius: 4,
        overflow: 'hidden',
        marginBottom: 10,
    },
    row: {
        flexDirection: 'row',
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: '#DFE3E6',
    },
    headerRow: {
        backgroundColor: '#F5F7FA',
    },
    headerContent: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
        paddingVertical: 8,
        paddingHorizontal: 12,
    },
    cell: {
        paddingHorizontal: 6,
        justifyContent: 'center',
        borderRightWidth: StyleSheet.hairlineWidth,
        borderRightColor: '#DFE3E6',
    },
    sortIndicator: {
        marginLeft: 4,
        width: 12,
    },
    headerText: {
        color: '#999',
        fontSize: 14,
    },
    expandButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 4,
    },
    expandButtonText: {
        color: '#666',
        fontSize: 12,
        marginRight: 4,
    },
    expandButtonIcon: {
        color: '#333',
        fontSize: 10,
    },
});

export default React.memo(MarkdownTable);
