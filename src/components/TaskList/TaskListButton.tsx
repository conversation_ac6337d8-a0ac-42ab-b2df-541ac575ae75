import {
    StyleSheet,
    TouchableOpacity,
    Animated,
    Easing,
    View,
    Text,
} from '@mrn/react-native';
import React, { useEffect, useRef, memo } from 'react';

import NetImages from '../../assets/images/homeRefactor';

interface TaskListButtonProps {
    /** 运行中任务数量 */
    runningCount: number;
    /** 是否展示完成图标 */
    showCompleted: boolean;
    /** 点击事件 */
    onPress: () => void;
    /** 是否加载中 */
    loading?: boolean;
}

const TaskListButton: React.FC<TaskListButtonProps> = ({
    runningCount,
    showCompleted,
    onPress,
    loading = false,
}) => {
    const rotationValue = useRef(new Animated.Value(0)).current;

    useEffect(() => {
        const loop = Animated.loop(
            Animated.timing(rotationValue, {
                toValue: 1,
                duration: 1200,
                easing: Easing.linear,
                useNativeDriver: true,
            }),
        );
        if (runningCount > 0 && !showCompleted) {
            loop.start();
        }
        return () => {
            loop.stop();
        };
    }, [runningCount, rotationValue, showCompleted]);

    const rotate = rotationValue.interpolate({
        inputRange: [0, 1],
        outputRange: ['0deg', '360deg'],
    });

    let imageSource;
    if (showCompleted) {
        imageSource = { uri: NetImages.taskSuccess };
    } else if (runningCount > 0) {
        imageSource = { uri: NetImages.taskRunning };
    } else {
        imageSource = { uri: NetImages.taskNormal };
    }

    return (
        <TouchableOpacity
            style={styles.container}
            onPress={onPress}
            disabled={loading}
            activeOpacity={0.7}
        >
            <Animated.Image
                source={imageSource}
                style={[
                    styles.image,
                    runningCount > 0 ? { transform: [{ rotate }] } : {},
                ]}
            />
            {runningCount > 0 && !showCompleted && (
                <View style={styles.countContainer}>
                    <Text style={styles.countText}>{runningCount}</Text>
                </View>
            )}
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    container: {
        width: 40,
        height: 40,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 8,
    },
    image: {
        width: 24,
        height: 24,
    },
    countContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        alignItems: 'center',
        justifyContent: 'center',
    },
    countText: {
        color: '#FFD100',
        fontSize: 10,
        fontWeight: 'bold',
        textAlign: 'center',
    },
});

export default memo(TaskListButton, (prevProps, nextProps) => {
    return (
        prevProps.runningCount === nextProps.runningCount &&
        prevProps.showCompleted === nextProps.showCompleted
    );
});
