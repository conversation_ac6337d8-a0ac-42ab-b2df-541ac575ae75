import {
    View,
    StyleSheet,
    Text,
    Image,
    TouchableOpacity,
} from '@mrn/react-native';
import React from 'react';

import GuideTooltip from '../GuideTooltip';

interface PoiItem {
    id: number;
    name: string;
    image: string;
}

interface PoiListGuideProps {
    visible: boolean;
    onClose: () => void;
    onComplete: () => void;
    poiList: PoiItem[];
    onPoiSignIn: (poiId: number) => void;
    onMorePress: () => void;
    children: React.ReactNode;
}

const PoiListGuide: React.FC<PoiListGuideProps> = ({
    visible,
    onClose,
    onComplete,
    poiList,
    onPoiSignIn,
    onMorePress,
    children,
}) => {
    const step = {
        id: 'poi_list',
        content: '小蜜发现附近有可签入商家',
        step: 3,
        totalSteps: 3,
        isLastStep: true,
    };

    return (
        <GuideTooltip
            visible={visible}
            step={step}
            onClose={onClose}
            onNext={() => {}}
            onComplete={onComplete}
        >
            <View style={styles.container}>
                {children}
                <View style={styles.poiListContainer}>
                    {poiList.map((poi) => (
                        <View key={poi.id} style={styles.poiItem}>
                            <Image
                                source={{ uri: poi.image }}
                                style={styles.poiImage}
                            />
                            <Text style={styles.poiName}>{poi.name}</Text>
                            <TouchableOpacity
                                style={styles.signInButton}
                                onPress={() => onPoiSignIn(poi.id)}
                            >
                                <Text style={styles.signInButtonText}>
                                    签入
                                </Text>
                            </TouchableOpacity>
                        </View>
                    ))}
                    <TouchableOpacity
                        style={styles.moreButton}
                        onPress={onMorePress}
                    >
                        <Text style={styles.moreButtonText}>
                            更多商家 {'>'}
                        </Text>
                    </TouchableOpacity>
                </View>
            </View>
        </GuideTooltip>
    );
};

const styles = StyleSheet.create({
    container: {
        position: 'relative',
    },
    poiListContainer: {
        backgroundColor: '#FFFEF0',
        borderRadius: 12,
        padding: 16,
        marginTop: 8,
        width: 280,
    },
    poiItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
    },
    poiImage: {
        width: 40,
        height: 40,
        borderRadius: 4,
    },
    poiName: {
        flex: 1,
        marginLeft: 12,
        fontSize: 14,
        color: '#333',
    },
    signInButton: {
        backgroundColor: '#4021FF',
        paddingVertical: 6,
        paddingHorizontal: 12,
        borderRadius: 16,
    },
    signInButtonText: {
        color: '#FFFFFF',
        fontSize: 14,
    },
    moreButton: {
        alignSelf: 'flex-end',
        marginTop: 8,
    },
    moreButtonText: {
        color: '#666',
        fontSize: 14,
    },
});

export default PoiListGuide;
