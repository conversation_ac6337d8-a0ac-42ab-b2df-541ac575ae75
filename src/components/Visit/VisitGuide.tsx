import { StyleSheet, View } from '@mrn/react-native';
import React from 'react';

import { GuideStep } from '../GuideTooltip';
import GuideManager from '../GuideTooltip/GuideManager';

// 拜访签入引导的步骤
const VISIT_GUIDE_STEPS: GuideStep[] = [
    {
        id: 'assistant_intro',
        content: '点击后进入小蜜，帮你智能分析、智能诊断等高频问题',
        step: 1,
        totalSteps: 3,
        // 移除固定的 placement，让组件自动计算最佳位置
    },
    {
        id: 'visit_checkin',
        content:
            '快捷签入&签出\n这里是拜访签入的快捷入口，点击后直接对商家进行签入，计时状态点击后直接签出',
        step: 2,
        totalSteps: 3,
        // 移除固定的 placement，让组件自动计算最佳位置
    },
    {
        id: 'visit_recommendation',
        content: '小蜜会及时提醒你对附近的商家进行签入，选中商家可直接签入',
        step: 3,
        totalSteps: 3,
        isLastStep: true,
        // 移除固定的 placement，让组件自动计算最佳位置
    },
];

interface VisitGuideProps {
    children: React.ReactNode[];
    enabled?: boolean;
    onComplete?: () => void;
}

const VisitGuide: React.FC<VisitGuideProps> = ({
    children,
    enabled = true,
    onComplete,
}) => {
    return (
        <View style={styles.container}>
            <GuideManager
                steps={VISIT_GUIDE_STEPS}
                children={children}
                guideKey="visit_checkin_guide"
                enabled={enabled}
                onComplete={onComplete}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        position: 'relative',
        zIndex: 9000, // 确保引导组件在高层级
    },
});

export default VisitGuide;
