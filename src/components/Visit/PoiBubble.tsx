import {
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from '@mrn/react-native';
import React, { useEffect, useRef, useState } from 'react';
import Svg, { Defs, LinearGradient, Path, Rect, Stop } from 'react-native-svg';

import DefaultImg from '../../assets/images/defaultImage.png';
import RNImage from '../RNImage';

import { SOURCE } from '@/types';
import { getMSILocation } from '@/utils/getLocation';

// 商家信息接口
export interface PoiItem {
    poiId: number;
    avatar?: string; // 商家头像
    name: string; // 商家名称
    distance?: string; // 距离，如 "100m"
    isCheckIn?: boolean; // 是否已签入
    checkInTime?: number; // 签入时间，毫秒时间戳
    url?: string; // 签出链接
}

interface PoiBubbleProps {
    // 通用属性
    bubbleType: 'signIn' | 'signOut'; // 气泡类型：签入或签出
    messagePosition?: 'left' | 'right'; // 气泡位置
    source: SOURCE; // 页面来源
    arrowTargetY?: number; // 箭头目标位置
    onClose?: () => void; // 关闭回调

    // 签入气泡属性
    poiList?: PoiItem[]; // 商家列表（签入气泡使用）
    onPoiSignIn?: (poiId: number) => void; // 签入回调
    onRefreshLocation?: (position: any) => void; // 刷新定位回调
    onMorePress?: () => void; // 更多商家回调

    // 签出气泡属性
    poiInfo?: PoiItem; // 商家信息（签出气泡使用）
    onSignOut?: (url: string) => void; // 签出回调
}

const PoiBubble: React.FC<PoiBubbleProps> = ({
    bubbleType,
    messagePosition = 'right',
    arrowTargetY,
    source,
    onClose,
    poiList = [],
    onPoiSignIn,
    onRefreshLocation,
    onMorePress,
    poiInfo,
    onSignOut,
}) => {
    // 倒计时相关状态（用于已签入状态下的倒计时显示）
    const [timeLeft, setTimeLeft] = useState<number>(0);
    const [isTimeout, setIsTimeout] = useState<boolean>(false);
    const timerRef = useRef<NodeJS.Timeout | null>(null);
    const timeoutMinutes = 60; // 超时时间（分钟），默认为60分钟
    const [address, setAddress] = useState(''); // 当前地址

    useEffect(() => {
        refreshLocation();
    }, []);

    const refreshLocation = async () => {
        const position = await getMSILocation();

        if (position.address) {
            console.log('debug address', position.address);
            setAddress(position.address);
        }

        return position;
    };

    // 计算剩余时间（秒）- 用于签出倒计时
    const calculateTimeLeft = () => {
        if (!poiInfo?.checkInTime) {
            return 0;
        }

        const now = Date.now();
        const elapsedTime = now - poiInfo.checkInTime; // 已经过去的时间（毫秒）
        const timeoutTime = timeoutMinutes * 60 * 1000; // 超时时间（毫秒）
        const remaining = timeoutTime - elapsedTime; // 剩余时间（毫秒）

        if (remaining <= 0) {
            setIsTimeout(true);
            return 0;
        }

        return Math.floor(remaining / 1000); // 转换为秒
    };

    // 格式化剩余时间为 MM:SS 格式
    const formatTimeLeft = (seconds: number) => {
        if (seconds <= 0) {
            return '00:00';
        }

        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;

        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds
            .toString()
            .padStart(2, '0')}`;
    };

    // 初始化倒计时
    useEffect(() => {
        if (bubbleType === 'signOut' && poiInfo?.checkInTime) {
            // 初始化剩余时间
            setTimeLeft(calculateTimeLeft());

            // 设置定时器，每秒更新一次
            timerRef.current = setInterval(() => {
                const remaining = calculateTimeLeft();
                setTimeLeft(remaining);

                if (remaining <= 0) {
                    // 清除定时器
                    if (timerRef.current) {
                        clearInterval(timerRef.current);
                        timerRef.current = null;
                    }
                }
            }, 1000);

            // 组件卸载时清除定时器
            return () => {
                if (timerRef.current) {
                    clearInterval(timerRef.current);
                    timerRef.current = null;
                }
            };
        }
    }, [bubbleType, poiInfo?.checkInTime]);

    // 渲染签入气泡内容
    const renderSignInContent = () => (
        <>
            {/* 标题 */}
            <View style={styles.signInTitle}>
                <Text style={styles.signInTitleText}>
                    小蜜发现附近有可签入商家
                </Text>
            </View>

            {/* 商家列表 */}
            <ScrollView style={styles.poiList}>
                {poiList.map((poi) => (
                    <View key={poi.poiId} style={styles.poiItem}>
                        {/* 商家头像 */}
                        {poi.avatar ? (
                            <RNImage
                                source={DefaultImg}
                                // source={{ uri: poi.avatar }}
                                style={styles.poiAvatar}
                            />
                        ) : (
                            <View
                                style={[styles.poiAvatar, styles.defaultAvatar]}
                            >
                                <Text style={styles.defaultAvatarText}>
                                    {poi.name.substring(0, 1)}
                                </Text>
                            </View>
                        )}

                        {/* 商家信息 */}
                        <View style={styles.poiInfo}>
                            <Text
                                style={styles.poiName}
                                numberOfLines={1}
                                ellipsizeMode="tail"
                            >
                                {poi.name}
                            </Text>
                        </View>

                        {/* 签入按钮 */}
                        <TouchableOpacity
                            style={styles.signInButton}
                            onPress={() => onPoiSignIn?.(poi.poiId)}
                        >
                            <Text style={styles.signInButtonText}>签入</Text>
                        </TouchableOpacity>
                    </View>
                ))}
            </ScrollView>

            {/* 底部操作区 */}
            <View style={styles.footer}>
                <TouchableOpacity
                    style={styles.footerButton}
                    onPress={async () => {
                        const position = await refreshLocation();
                        onRefreshLocation?.(position);
                    }}
                >
                    <Svg width={16} height={16} viewBox="0 0 24 24">
                        <Path
                            d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"
                            fill="#666"
                        />
                    </Svg>
                    <Text style={styles.footerButtonText}>
                        {address || '定位位置'}
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity
                    style={styles.footerMoreButton}
                    onPress={onMorePress}
                >
                    <Text style={styles.footerButtonText}>更多商家</Text>
                    <Svg width={16} height={16} viewBox="0 0 24 24">
                        <Path
                            d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"
                            fill="#666"
                        />
                    </Svg>
                </TouchableOpacity>
            </View>
        </>
    );

    // 渲染签出气泡内容
    const renderSignOutContent = () => {
        if (!poiInfo) {
            return null;
        }

        return (
            <>
                <View style={styles.contentContainer}>
                    {/* 商家头像 */}
                    {poiInfo.avatar ? (
                        <RNImage
                            source={DefaultImg}
                            // source={{ uri: poiInfo.avatar }}
                            style={styles.poiAvatar}
                        />
                    ) : (
                        <View style={[styles.poiAvatar, styles.defaultAvatar]}>
                            <Text style={styles.defaultAvatarText}>
                                {poiInfo.name.substring(0, 1)}
                            </Text>
                        </View>
                    )}

                    {/* 商家名称和提示文字 */}
                    <TouchableOpacity
                        onPress={() => onSignOut?.(poiInfo.url || '')}
                    >
                        <View style={styles.textContainer}>
                            <Text
                                style={styles.poiName}
                                numberOfLines={1}
                                ellipsizeMode="tail"
                            >
                                {poiInfo.name}
                            </Text>
                            <Text style={styles.reminderText}>
                                记得签出哦～
                            </Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </>
        );
    };

    // 渲染已签入标识
    const renderCheckInBadge = () => {
        // TODO: 联调阶段SOURCE.home需要替换为商家详情页
        if (
            source === SOURCE.home &&
            !poiInfo?.checkInTime &&
            bubbleType === 'signIn'
        ) {
            return (
                <TouchableOpacity
                    onPress={() => {
                        console.log('debug 调用拜访签入接口');
                    }}
                    style={[
                        styles.checkInBadge,
                        messagePosition === 'left' ? {} : { right: -58 },
                    ]}
                >
                    <Text style={styles.checkInText} numberOfLines={1}>
                        去签到
                    </Text>
                </TouchableOpacity>
            );
        }

        if (!poiInfo?.checkInTime || bubbleType !== 'signOut') {
            return null;
        }

        return isTimeout ? null : (
            <TouchableOpacity
                onPress={() => {
                    console.log('debug 跳转拜访签出页面');
                }}
                style={[
                    styles.checkInBadge,
                    messagePosition === 'left' ? {} : { right: -58 },
                ]}
            >
                <Text style={styles.checkInText} numberOfLines={1}>
                    {formatTimeLeft(timeLeft)}
                </Text>
            </TouchableOpacity>
        );
    };

    return (
        <>
            <View
                style={[
                    styles.container,
                    // 根据messagePosition调整气泡的位置
                    messagePosition === 'left'
                        ? { right: 'auto', left: 22 }
                        : { left: 'auto', right: 22 },
                ]}
            >
                {/* 渐变背景 */}
                <View style={styles.gradientContainer}>
                    <Svg
                        width="100%"
                        height="100%"
                        style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                        }}
                        preserveAspectRatio="none"
                        viewBox="0 0 100 100"
                    >
                        <Defs>
                            <LinearGradient
                                id="bubbleGradient"
                                x1="30%"
                                y1="40%"
                                x2="0%"
                                y2="0%"
                            >
                                <Stop offset="0%" stopColor="#FFF" />
                                <Stop offset="100%" stopColor="#D2FFDC" />
                            </LinearGradient>
                        </Defs>
                        <Rect
                            x="0"
                            y="0"
                            width="100"
                            height="100"
                            fill="url(#bubbleGradient)"
                        />
                    </Svg>
                </View>

                {/* 箭头 - 左侧 */}
                {messagePosition === 'left' ? (
                    <View
                        style={[
                            styles.arrowContainer,
                            styles.arrowContainerLeft,
                            {
                                top:
                                    arrowTargetY !== undefined
                                        ? arrowTargetY - 8
                                        : '50%',
                                marginTop: arrowTargetY !== undefined ? 0 : -8,
                            },
                        ]}
                    >
                        <Svg width={8} height={16} viewBox="0 0 8 16">
                            <Path
                                d="M8 0 Q2 4 0 8 Q2 12 8 16 Z"
                                fill={'#FFFEF0'}
                            />
                        </Svg>
                    </View>
                ) : null}

                {/* 内容区域 */}
                <View style={styles.contentWrapper}>
                    {bubbleType === 'signIn' ? (
                        <TouchableOpacity
                            style={styles.closeButton}
                            onPress={onClose}
                        >
                            <Text style={styles.closeText}>×</Text>
                        </TouchableOpacity>
                    ) : null}

                    {/* 根据气泡类型渲染不同内容 */}
                    {bubbleType === 'signIn'
                        ? renderSignInContent()
                        : renderSignOutContent()}
                </View>

                {/* 箭头 - 右侧 */}
                {messagePosition === 'right' ? (
                    <View
                        style={[
                            styles.arrowContainer,
                            {
                                top:
                                    arrowTargetY !== undefined
                                        ? arrowTargetY - 8
                                        : '50%',
                                marginTop: arrowTargetY !== undefined ? 0 : -8,
                            },
                        ]}
                    >
                        <Svg width={8} height={16} viewBox="0 0 8 16">
                            <Path
                                d="M0 0 Q6 4 8 8 Q6 12 0 16 Z"
                                fill={'#FFFEF0'}
                            />
                        </Svg>
                    </View>
                ) : null}
            </View>

            {/* 已签入标识 */}
            {renderCheckInBadge()}
        </>
    );
};

const styles = StyleSheet.create({
    container: {
        position: 'absolute',
        borderRadius: 16,
        elevation: 5,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        zIndex: 999, // 降低层级，确保不会覆盖引导弹窗
    },
    gradientContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        borderRadius: 16,
        overflow: 'hidden',
    },
    contentWrapper: {
        padding: 14,
        zIndex: 1,
        position: 'relative',
    },
    closeButton: {
        position: 'absolute',
        top: 8,
        right: 14,
        width: 24,
        height: 40,
        borderRadius: 12,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 2,
    },
    closeText: {
        paddingTop: 4,
        fontSize: 32,
        fontWeight: '300',
        color: '#292D32',
    },
    // 签入气泡样式
    signInTitle: {
        paddingVertical: 8,
        marginBottom: 8,
        paddingRight: 40,
    },
    signInTitleText: {
        fontSize: 16,
        fontWeight: '500',
        color: '#333',
    },
    poiList: {
        maxHeight: 200,
    },
    poiItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 4,
    },
    poiAvatar: {
        width: 36,
        height: 36,
        borderRadius: 7,
        marginRight: 6,
    },
    defaultAvatar: {
        backgroundColor: '#FF6B6B',
        justifyContent: 'center',
        alignItems: 'center',
    },
    defaultAvatarText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '600',
    },
    poiInfo: {
        flex: 1,
    },
    poiName: {
        fontSize: 14,
        fontWeight: '500',
        color: '#777',
    },
    poiDistance: {
        fontSize: 12,
        color: '#999',
        marginTop: 4,
    },
    signInButton: {
        backgroundColor: '#4021FF',
        borderRadius: 20,
        paddingVertical: 9,
        paddingHorizontal: 14,
    },
    signInButtonText: {
        color: '#fff',
        fontSize: 14,
        fontWeight: '500',
    },
    footer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: 4,
        paddingTop: 4,
        borderTopColor: '#f0f0f0',
    },
    footerButton: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    footerButtonText: {
        fontSize: 14,
        color: '#666',
        marginLeft: 4,
    },
    footerMoreButton: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    // 签出气泡样式
    contentContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    textContainer: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    reminderText: {
        paddingLeft: 4,
        fontSize: 15,
        fontWeight: '600',
        color: '#333',
    },
    // 箭头样式
    arrowContainer: {
        position: 'absolute',
        right: -8,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        zIndex: 2,
    },
    arrowContainerLeft: {
        right: 'auto',
        left: -8,
    },
    // 已签入标识样式
    checkInBadge: {
        position: 'absolute',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        bottom: -90,
        right: -4,
        height: 40,
        width: 60,
        borderWidth: 2,
        borderColor: '#FFF',
        backgroundColor: '#F8FEE7',
        borderRadius: 24,
        shadowColor: '#0000001E',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.8,
        shadowRadius: 4,
        elevation: 5,
        zIndex: 10,
    },
    checkInText: {
        fontSize: 14,
        fontWeight: '500',
        color: '#FF6600',
    },
});

export default PoiBubble;
