import { View } from '@mrn/react-native';
import React, { useState, useEffect } from 'react';

import GuideTooltip, { GuideStep } from './index';
import { getGuideStatus, setGuideStatus } from '../../api/guideApi';

interface GuideManagerProps {
    steps: GuideStep[];
    children: React.ReactNode[];
    guideKey: string; // 用于存储引导状态的唯一键
    enabled?: boolean; // 是否启用引导
    onComplete?: () => void; // 完成所有引导步骤后的回调
}

const GuideManager: React.FC<GuideManagerProps> = ({
    steps,
    children,
    guideKey,
    enabled = true,
    onComplete,
}) => {
    const [currentStepIndex, setCurrentStepIndex] = useState<number>(-1);
    const [guideCompleted, setGuideCompleted] = useState<boolean>(false);

    // 检查引导是否已完成
    useEffect(() => {
        const checkGuideStatus = async () => {
            try {
                const completed = await getGuideStatus(guideKey);
                if (completed) {
                    setGuideCompleted(true);
                } else if (enabled) {
                    setCurrentStepIndex(0);
                }
            } catch (error) {
                console.error('Error checking guide status:', error);
            }
        };

        checkGuideStatus();
    }, [guideKey, enabled]);

    // 处理关闭引导
    const handleClose = () => {
        setCurrentStepIndex(-1);
    };

    // 处理下一步
    const handleNext = () => {
        if (currentStepIndex < steps.length - 1) {
            setCurrentStepIndex(currentStepIndex + 1);
        }
    };

    // 处理完成引导
    const handleComplete = async () => {
        try {
            await setGuideStatus(guideKey, true);
            setGuideCompleted(true);
            setCurrentStepIndex(-1);
            onComplete?.();
        } catch (error) {
            console.error('Error saving guide status:', error);
        }
    };

    // 重置引导状态（用于测试）
    // const resetGuide = async () => {
    //     try {
    //         await resetGuideStatus(guideKey);
    //         setGuideCompleted(false);
    //         setCurrentStepIndex(0);
    //     } catch (error) {
    //         console.error('Error resetting guide status:', error);
    //     }
    // };

    // 如果引导已完成，直接渲染子组件而不显示引导
    if (guideCompleted) {
        return (
            <>
                {children.map((child, index) => (
                    <View key={index} style={{ display: 'flex' }}>
                        {child}
                    </View>
                ))}
            </>
        );
    }

    return (
        <>
            {children.map((child, index) => {
                const isCurrentStep = index === currentStepIndex;
                const step = steps[index];

                return (
                    <View key={step?.id || index} style={{ display: 'flex' }}>
                        {isCurrentStep && step ? (
                            <GuideTooltip
                                visible={isCurrentStep}
                                step={step}
                                onClose={handleClose}
                                onNext={handleNext}
                                onComplete={handleComplete}
                            >
                                {child}
                            </GuideTooltip>
                        ) : (
                            child
                        )}
                    </View>
                );
            })}
        </>
    );
};

export default GuideManager;
