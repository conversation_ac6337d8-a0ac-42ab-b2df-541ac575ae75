import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    Dimensions,
} from '@mrn/react-native';
import React, { useState, useEffect, useRef } from 'react';
import Tooltip from 'react-native-walkthrough-tooltip';

export interface GuideStep {
    id: string;
    content: string;
    placement?: 'top' | 'bottom' | 'left' | 'right';
    step: number;
    totalSteps: number;
    buttonText?: string;
    isLastStep?: boolean;
}

interface GuideTooltipProps {
    visible: boolean;
    step: GuideStep;
    onClose: () => void;
    onNext: () => void;
    onComplete: () => void;
    children: React.ReactNode;
    backgroundColor?: string;
    textColor?: string;
    buttonColor?: string;
    buttonTextColor?: string;
}

const GuideTooltip: React.FC<GuideTooltipProps> = ({
    visible,
    step,
    onClose,
    onNext,
    onComplete,
    children,
    backgroundColor = '#FFF',
    textColor = '#222',
    buttonColor = '#4021FF',
    buttonTextColor = '#FFFFFF',
}) => {
    const [tooltipVisible, setTooltipVisible] = useState(false);
    const [placement, setPlacement] = useState<
        'top' | 'bottom' | 'left' | 'right'
    >(step.placement || 'bottom');
    const childRef = useRef<View>(null);
    const { width: screenWidth, height: screenHeight } =
        Dimensions.get('window');
    const tooltipWidth = 234; // 弹窗宽度
    const tooltipHeight = 150; // 估计的弹窗高度，可以根据实际情况调整
    const margin = 20; // 边缘安全距离

    // 根据目标组件位置自动调整弹窗方向
    useEffect(() => {
        if (childRef.current) {
            childRef.current.measure((x, y, width, height, pageX, pageY) => {
                // 计算目标组件中心点
                const centerX = pageX + width / 2;
                const centerY = pageY + height / 2;

                // 计算目标组件在屏幕中的位置
                const isLeft = centerX < screenWidth / 3;
                const isRight = centerX > (screenWidth * 2) / 3;
                const isTop = centerY < screenHeight / 3;
                const isBottom = centerY > (screenHeight * 2) / 3;

                // 根据位置决定弹窗方向
                let newPlacement: 'top' | 'bottom' | 'left' | 'right';

                if (isLeft) {
                    // 目标在左侧，弹窗在右侧
                    newPlacement = 'right';
                    // 检查右侧空间是否足够
                    if (pageX + width + tooltipWidth + margin > screenWidth) {
                        newPlacement = 'top';
                    }
                } else if (isRight) {
                    // 目标在右侧，弹窗在左侧
                    newPlacement = 'left';
                    // 检查左侧空间是否足够
                    if (pageX - tooltipWidth - margin < 0) {
                        newPlacement = 'top';
                    }
                } else if (isTop) {
                    // 目标在顶部，弹窗在底部
                    newPlacement = 'bottom';
                    // 检查底部空间是否足够
                    if (
                        pageY + height + tooltipHeight + margin >
                        screenHeight
                    ) {
                        newPlacement = 'top';
                    }
                } else if (isBottom) {
                    // 目标在底部，弹窗在顶部
                    newPlacement = 'top';
                    // 检查顶部空间是否足够
                    if (pageY - tooltipHeight - margin < 0) {
                        newPlacement = 'bottom';
                    }
                } else {
                    // 目标在中间，优先底部
                    newPlacement = 'bottom';
                    // 检查底部空间是否足够
                    if (
                        pageY + height + tooltipHeight + margin >
                        screenHeight
                    ) {
                        newPlacement = 'top';
                    }
                }

                setPlacement(newPlacement);
            });
        }
    }, [visible, screenWidth, screenHeight]);

    useEffect(() => {
        setTooltipVisible(visible);
    }, [visible]);

    const handleButtonPress = () => {
        if (step.isLastStep) {
            onComplete();
        } else {
            onNext();
        }
    };

    return (
        <View ref={childRef} collapsable={false}>
            <Tooltip
                isVisible={tooltipVisible}
                content={
                    <View style={styles.tooltipContainer}>
                        <Text style={[styles.content, { color: textColor }]}>
                            {step.content}
                        </Text>
                        <View style={styles.footer}>
                            <Text
                                style={[
                                    styles.stepIndicator,
                                    { color: textColor },
                                ]}
                            >
                                {step.step}
                                <Text style={{ color: '#999' }}>
                                    /{step.totalSteps}
                                </Text>
                            </Text>
                            <TouchableOpacity
                                style={[
                                    styles.button,
                                    { backgroundColor: buttonColor },
                                ]}
                                onPress={handleButtonPress}
                            >
                                <Text
                                    style={[
                                        styles.buttonText,
                                        { color: buttonTextColor },
                                    ]}
                                >
                                    {step.isLastStep
                                        ? '完成'
                                        : step.buttonText || '下一步'}
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                }
                placement={placement}
                onClose={onClose}
                contentStyle={[styles.tooltip, { backgroundColor }]}
                arrowSize={{ width: 16, height: 8 }}
                backgroundStyle={styles.tooltipBackground}
                tooltipStyle={styles.tooltipContainer}
            >
                {children}
            </Tooltip>
        </View>
    );
};

const styles = StyleSheet.create({
    tooltipContainer: {
        paddingRight: 12,
        zIndex: 9999, // 确保引导弹窗在最上层
        elevation: 9999, // Android 平台的层级
    },
    tooltip: {
        borderRadius: 12,
        paddingHorizontal: 16,
        zIndex: 9999,
        elevation: 9999,
        width: 234, // 设置固定宽度为234
    },
    tooltipBackground: {
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        zIndex: 9998, // 蒙层层级略低于弹窗
        elevation: 9998,
    },
    content: {
        fontSize: 16,
        lineHeight: 24,
        marginBottom: 16,
    },
    footer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: 8,
    },
    stepIndicator: {
        fontSize: 16,
    },
    button: {
        marginRight: -4,
        padding: 9,
        borderRadius: 20,
    },
    buttonText: {
        fontSize: 14,
        fontWeight: '500',
    },
});

export default GuideTooltip;
