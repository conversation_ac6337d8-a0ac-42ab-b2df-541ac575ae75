import {
    Image,
    Keyboard,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import { useRequest } from 'ahooks';
import React, { useState, Fragment } from 'react';

import AssociationImg from '../assets/images/association.png';
import { useBizInfo } from '../hooks/useBizInfo';
import useCallerRequest from '../hooks/useCallerRequest';
import useMessage from '../hooks/useMessage';
import { useSendMessage } from '../hooks/useSendMessage';
import { EntryPointType } from '../types';

import { trackEvent, TrackEventType } from '@/utils/track';

const styles = StyleSheet.create({
    container: {
        marginTop: 2,
        paddingTop: 16,
        paddingBottom: 12,
        borderTopLeftRadius: 10.5,
        borderTopRightRadius: 10.5,
        backgroundColor: '#fff',
        marginBottom: -52,
        zIndex: 10,
    },
    scrollView: {
        paddingHorizontal: 16,
        borderBottomColor: '#eee',
        borderBottomWidth: 0.5,
    },
    title: {
        paddingHorizontal: 16,
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    image: {
        width: 70,
        height: 20,
    },
    item: {
        paddingVertical: 12,
    },
    itemBorder: {
        borderBottomColor: '#eee',
        borderBottomWidth: 0.5,
    },
    highlight: {
        color: '#FF6A00',
    },
    shadow: {
        ...Platform.select({
            ios: {
                shadowColor: '#171717',
                shadowOffset: {
                    width: 2,
                    height: 2,
                },
                shadowOpacity: 0.9,
                shadowRadius: 8,
            },
            android: {
                elevation: 5,
                zIndex: 5,
                backgroundColor: '#fff',
            },
        }),
    },
});

const InputAssociation = () => {
    const { text, clear } = useMessage((state) => state.input);

    const [visible, setVisible] = useState(false);
    const { send } = useSendMessage();
    const checkIsPolling = useMessage((state) => state.checkIsPolling);
    const { bizId } = useBizInfo();

    const callerRequest = useCallerRequest();
    const fetchAssociation = async () => {
        if (text.length < 2) {
            return;
        }

        setVisible(true);
        const res = await callerRequest.get(
            '/bee/v1/bdaiassistant/getRelatedQuestion',
            { input: text, bizId },
            { silent: true },
        );

        if (res.code !== 0) {
            return;
        }

        return res.data.questions;
    };

    const { data = [], mutate } = useRequest(fetchAssociation, {
        refreshDeps: [text],
        throttleWait: 300,
    });

    const onPress = (content: string) => {
        if (checkIsPolling()) {
            return;
        }
        mutate([]);
        clear();
        trackEvent(
            'chat_association',
            { question: text, msg_content: content },
            TrackEventType.MC,
            {},
        );
        send(content, EntryPointType.ASSOCIATION);
        Keyboard.dismiss();
    };

    const file = useMessage((state) => state.file);
    if (!visible || !data.length || file.length > 0) {
        return null;
    }

    return (
        <>
            <View style={[styles.container]}>
                <View style={styles.title}>
                    <Image source={AssociationImg} style={styles.image} />
                    <TouchableOpacity onPress={() => setVisible(false)}>
                        <Icon size={14} type="close" tintColor="#000000" />
                    </TouchableOpacity>
                </View>

                <ScrollView
                    contentContainerStyle={styles.scrollView}
                    keyboardShouldPersistTaps={'handled'}
                >
                    {data.map((d, index) => (
                        <TouchableOpacity
                            style={[
                                styles.item,
                                index < data.length - 1
                                    ? styles.itemBorder
                                    : undefined,
                            ]}
                            key={d}
                            onPress={() => onPress(d)}
                        >
                            <Text numberOfLines={1}>
                                {d.split(text).map((it, i, arr) => (
                                    <Fragment key={`${it}_${i}`}>
                                        <Text>{it}</Text>
                                        {i < arr.length - 1 ? (
                                            <Text style={styles.highlight}>
                                                {text}
                                            </Text>
                                        ) : null}
                                    </Fragment>
                                ))}
                            </Text>
                        </TouchableOpacity>
                    ))}
                </ScrollView>
            </View>
        </>
    );
};

export default InputAssociation;
