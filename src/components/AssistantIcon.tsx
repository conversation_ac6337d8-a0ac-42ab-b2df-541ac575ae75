import { Image, ImageProps, ImageStyle } from '@mrn/react-native';
import React from 'react';

import NetImages from '../assets/images/homeRefactor';

interface AssistantIcon extends Omit<ImageProps, 'source'> {
    style?: ImageStyle;
    size?: number;
    noText?: boolean;
}

const AssistantIcon = (props: AssistantIcon) => {
    const { style, size = 55, ...rest } = props;

    return (
        <Image
            {...rest}
            source={{
                uri: props.noText
                    ? NetImages.defaultIconWithoutSpace
                    : NetImages.defaultGifIcon,
            }}
            style={[{ height: size, width: size }, style]}
        />
    );
};

export default AssistantIcon;
