import { StyleSheet, Text, TouchableOpacity, View } from '@mrn/react-native';
import React, { useState } from 'react';
import Tooltip from 'react-native-walkthrough-tooltip';

interface WalkthroughGuideProps {
    visible: boolean;
    children: React.ReactNode;
    onClose: () => void;
    onComplete: () => void;
}

const WalkthroughGuide: React.FC<WalkthroughGuideProps> = ({
    visible,
    children,
    onClose,
    onComplete,
}) => {
    const [currentStep, setCurrentStep] = useState(0);
    const totalSteps = 4;

    const handleNext = () => {
        if (currentStep < totalSteps - 1) {
            setCurrentStep(currentStep + 1);
        } else {
            onComplete();
        }
    };

    const handleSkip = () => {
        onClose();
    };

    const renderTooltipContent = () => (
        <View style={styles.tooltipContent}>
            <View style={styles.guideCard}>
                <Text style={styles.guideTitle}>智能沟通</Text>
                <Text style={styles.guideDescription}>
                    这里是商家智能沟通工具：
                </Text>
                <Text style={styles.guideDescription}>
                    轻松发起商家批量沟通
                </Text>
            </View>

            <View style={styles.bottomContainer}>
                <View style={styles.indicatorContainer}>
                    <Text style={styles.stepIndicator}>
                        {currentStep + 1}/{totalSteps}
                    </Text>
                </View>

                <View style={styles.buttonContainer}>
                    <TouchableOpacity
                        style={styles.skipButton}
                        onPress={handleSkip}
                    >
                        <Text style={styles.skipButtonText}>跳过</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={styles.nextButton}
                        onPress={handleNext}
                    >
                        <Text style={styles.nextButtonText}>
                            {currentStep < totalSteps - 1 ? '下一步' : '完成'}
                        </Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );

    return (
        <Tooltip
            isVisible={visible}
            content={renderTooltipContent()}
            placement="bottom"
            onClose={onClose}
            showChildInTooltip={true}
            allowChildInteraction={false}
            backgroundColor="rgba(0, 0, 0, 0.6)"
            contentStyle={styles.tooltipContainer}
            arrowStyle={styles.arrow}
        >
            {children}
        </Tooltip>
    );
};

const styles = StyleSheet.create({
    tooltipContainer: {
        backgroundColor: 'transparent',
        padding: 0,
        borderRadius: 0,
        shadowOpacity: 0,
        elevation: 0,
    },
    tooltipContent: {
        paddingHorizontal: 20,
        paddingVertical: 20,
        alignItems: 'center',
    },
    guideCard: {
        backgroundColor: '#FFFFFF',
        borderRadius: 12,
        padding: 20,
        width: '100%',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 3,
        marginBottom: 20,
    },
    guideTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#FF6A00',
        marginBottom: 12,
        textAlign: 'left',
    },
    guideDescription: {
        fontSize: 14,
        color: '#333333',
        lineHeight: 20,
        textAlign: 'left',
    },
    bottomContainer: {
        width: '100%',
    },
    indicatorContainer: {
        alignItems: 'center',
        marginBottom: 20,
    },
    stepIndicator: {
        fontSize: 14,
        color: '#FFFFFF',
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    skipButton: {
        paddingHorizontal: 20,
        paddingVertical: 12,
    },
    skipButtonText: {
        fontSize: 16,
        color: '#FFFFFF',
    },
    nextButton: {
        backgroundColor: '#4021FF',
        borderRadius: 20,
        paddingHorizontal: 24,
        paddingVertical: 12,
        elevation: 5,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3,
    },
    nextButtonText: {
        fontSize: 16,
        color: '#FFFFFF',
        fontWeight: '500',
    },
    arrow: {
        display: 'none',
    },
});

export default WalkthroughGuide;
