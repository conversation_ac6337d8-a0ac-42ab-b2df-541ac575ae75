import React from 'react';

import { cast, renderEle } from './textMessageUtils';
import { MessageStatus } from '../../../../types';

const StandardQuestion = ({
    children,
    data,
    status,
    msgId,
    history,
    listRef,
    textColor = '#222',
}: {
    children: any[];
    status: MessageStatus;
    msgId: string;
    history: boolean;
    listRef: any;
    shouldTyping: boolean;
    data: any;
    textColor: string;
}) => {
    return (
        <>
            {cast(
                children.map(renderEle(data, textColor)),
                status,
                msgId,
                history,
                listRef,
            )}
        </>
    );
};
export default StandardQuestion;
