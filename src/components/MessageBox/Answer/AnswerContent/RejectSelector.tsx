import { openMRN } from '@mfe/bee-foundation-utils';
import {
    View,
    Text,
    TouchableOpacity,
    StyleProp,
    ViewStyle,
    Image,
    ScrollView,
    Dimensions,
} from '@mrn/react-native';
import React, { useState } from 'react';

import CloseImg from '../../../../assets/images/poiSelector/closeBlack.png';
import RightImg from '../../../../assets/images/selector/right.png';
import { useSendMessage } from '../../../../hooks/useSendMessage';
import { useSlideModalState } from '../../../../store/slideModal';
import TWS from '../../../../TWS';
import { EntryPointType } from '../../../../types';
import {
    Config,
    SelectorItemMessage,
    SelectorMessage,
} from '../../../../types/message';
import Condition from '../../../Condition/Condition';
import ViewWithLayout, {
    LayoutContext,
} from '../../../ViewWithLayout/ViewWithLayout';

import { PoiItem } from '@/components/Chat/PoiSelector';

const getItemFromArr = (
    arr: SelectorItemMessage['insert']['selectorItem']['content'],
    key: string,
) => {
    return arr.find((v) => v.key === key)?.value;
};

type SelectorItemData =
    SelectorMessage['insert']['selector']['content'][number];
export const SelectorItemContent = ({ data }: { data: SelectorItemData }) => {
    return (
        <>
            <Text
                numberOfLines={2}
                ellipsizeMode={'tail'}
                style={{
                    fontSize: 16,
                    fontWeight: '500',
                    color: '#222',
                    marginBottom: 10,
                }}
            >
                {data.title}
            </Text>
            {data.content
                .filter((v) => v.show)
                .map(({ label, value }) => {
                    return (
                        <Text style={{ fontSize: 14, color: '#666' }}>
                            {label}: {value}
                        </Text>
                    );
                })}
        </>
    );
};
const SelectorItem = ({
    data,
    style,
    close = () => {},
}: {
    data: SelectorItemData;
    style?: StyleProp<ViewStyle>;
    close?: () => void;
}) => {
    const { send } = useSendMessage();

    if (data.type === 'poi_private') {
        return (
            <PoiItem
                name={data.title}
                url={getItemFromArr(data.content, 'avatar') as string}
                online={getItemFromArr(data.content, 'online') === 'true'}
                labels={data.content.filter((v) =>
                    [undefined, 'label', 'ID'].includes(v.key),
                )}
                tags={data.content
                    .filter((v) => v.key === 'tag')
                    .map((v) => v.value as string)}
                isLast={false}
                onPress={() => {
                    const id = data.content.find((v) => v.key === 'ID')?.value;
                    if (!id) {
                        return;
                    }
                    openMRN({
                        mrn_biz: 'waimai',
                        mrn_entry: 'waimai-mfe-bee',
                        initialRoute: 'CompanyDetail',
                        mrn_component: 'private-sea',
                        data: {
                            wmPoiId: id,
                        },
                    });
                }}
            />
        );
    }
    return (
        <TouchableOpacity
            onPress={() => {
                send(
                    JSON.stringify([
                        {
                            type: 'config',
                            insert: {
                                config: {
                                    style: {
                                        backgroundColor: '#fff',
                                        width: '100%',
                                    },
                                },
                            },
                        } as Config,
                        {
                            type: 'selectorItem',
                            insert: {
                                selectorItem: data,
                            },
                        } as SelectorItemMessage,
                    ]),
                    EntryPointType.REJECT_SELECTOR,
                );
                close();
            }}
            style={[
                {
                    borderWidth: 0.5,
                    borderColor: '#E2E2E2',
                    borderRadius: 6,
                    padding: 12,
                },
                style,
            ]}
        >
            <SelectorItemContent data={data} />
        </TouchableOpacity>
    );
};

const ModalContent = ({ content, titleInSelector, close }) => {
    const [filteredContent, _] = useState(content);

    return (
        <View style={{ padding: 10, height: 478 }}>
            <View style={[TWS.row(), { justifyContent: 'space-between' }]}>
                <Text />
                <Text style={{ fontSize: 14, color: '#222' }}>
                    {titleInSelector}
                </Text>
                <TouchableOpacity onPress={close}>
                    <Image source={CloseImg} style={TWS.square(16)} />
                </TouchableOpacity>
            </View>
            <View
                style={[
                    TWS.line({ width: Dimensions.get('window').width }),
                    { marginLeft: -10, marginBottom: 12, marginTop: 12 },
                ]}
            />
            {/* <SearchBar
                onChange={(v: string) => {
                    if (!v) {
                        setFilteredContent(content);
                    }
                    setFilteredContent(
                        content.filter(
                            ({ title, content }) =>
                                title.includes(v) ||
                                content.some(({ value }) => value.includes(v)),
                        ),
                    );
                }}
            /> */}
            <ScrollView showsVerticalScrollIndicator={false}>
                {filteredContent.map((v) => {
                    return (
                        <SelectorItem
                            data={v}
                            style={{ marginBottom: 10 }}
                            close={close}
                        />
                    );
                })}
            </ScrollView>
        </View>
    );
};
const Selector = ({ data }: { data: SelectorMessage }) => {
    const {
        insert: {
            selector: {
                showNum = 2,
                content,
                extendButtonName = '查看更多',
                titleInIm,
                titleInSelector,
                showDivider = true,
            },
        },
    } = data;
    const { open, close } = useSlideModalState();

    const onPress = () => {
        open(
            <ModalContent
                close={close}
                content={content}
                titleInSelector={titleInSelector}
            />,
        );
    };
    return (
        <ViewWithLayout>
            <Text
                style={{ fontSize: 14, color: '#222', lineHeight: 20 }}
                selectable
            >
                {titleInIm}
            </Text>
            <Condition condition={[showDivider]}>
                <LayoutContext.Consumer>
                    {(containerLayout) => {
                        return (
                            <View
                                style={[
                                    TWS.line({
                                        width: containerLayout.width + 32,
                                    }),
                                    {
                                        marginBottom: 12,
                                        marginTop: 12,
                                        // 容器有padding，所以需要左移
                                        marginLeft: -16,
                                    },
                                ]}
                            />
                        );
                    }}
                </LayoutContext.Consumer>
            </Condition>
            {content.slice(0, showNum).map((v) => {
                return <SelectorItem data={v} style={{ marginBottom: 10 }} />;
            })}
            <Condition condition={[showNum < content.length]}>
                <TouchableOpacity
                    style={[TWS.row(), TWS.center()]}
                    onPress={onPress}
                >
                    <Text style={{ color: '#FF6A00', fontSize: 14 }}>
                        {extendButtonName}
                    </Text>
                    <Image source={RightImg} style={TWS.square(16)} />
                </TouchableOpacity>
            </Condition>
        </ViewWithLayout>
    );
};
export default Selector;
