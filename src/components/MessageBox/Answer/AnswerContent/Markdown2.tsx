import React from 'react';

import { MemoizedMarkdownInner } from './MemoizedMarkdownInner';
import { splitMediaFromMarkdown } from '../../../../utils/delta2message';
import ImageGallery from '../../../ImageGallery/ImageGallery';
import MarkdownTable from '../../../MarkdownTable';

// 基于react-native-markdown-display的md展示容器
const MemoizedMarkdown = React.memo(
    ({
        children,
        tableTitle,
        showRotateButton,
    }: {
        children: string[];
        tableTitle: string;
        showRotateButton?: boolean;
    }) => {
        // 处理表格：将整个格作为一个整体
        const processContent = (text: string) => {
            const lines = text.split('\n');
            const result: string[] = [];
            let currentTable: string[] = [];
            let isInTable = false;
            let otherLines: string[] = [];

            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                const isTableLine = /^\|/.test(line);

                if (isTableLine) {
                    if (!isInTable) {
                        isInTable = true;
                        result.push(otherLines.join('\n'));
                        otherLines = [];
                    }
                    currentTable.push(line);
                } else {
                    otherLines.push(line);
                    if (isInTable) {
                        // 表格结束，将整个表格作为一个整体
                        result.push(currentTable.join('\n'));
                        currentTable = [];
                        isInTable = false;
                    }
                }
            }

            // 处理最后一个表格
            if (currentTable.length > 0) {
                result.push(currentTable.join('\n'));
            }
            if (otherLines.length > 0) {
                result.push(otherLines.join('\n'));
            }

            return result;
        };

        // 蜜蜂端由于margin不会合并，所以参考链接前不需要额外的空行，所以需要手动剔除&nbsp;
        const originText = children
            .join('')
            .replace(/&nbsp;/g, '')
            .replace(/(\S)\*\*_/g, '$1 **_')
            .replace(/_\*\*(\S)/g, '_** $1');

        const parsedData1: any[] = splitMediaFromMarkdown(originText);
        const parsedData2 = parsedData1
            .map((v) => {
                if (v.type !== 'text') {
                    return [v];
                }
                const str = v.insert;
                const processedContent = processContent(str);
                return processedContent.map((v) => {
                    const isTable = /^\|.*/.test(v.split('\n')[0]);
                    if (isTable) {
                        return {
                            type: 'table',
                            insert: v,
                        };
                    }
                    return {
                        type: 'text',
                        insert: v,
                    };
                });
            })
            .reduce((acc, cur) => {
                return [...acc, ...cur];
            }, []);

        return (
            <>
                {parsedData2.map((v, i) => {
                    if (v.type === 'media') {
                        return <ImageGallery images={v.insert.media} key={i} />;
                    }
                    if (v.type === 'table') {
                        return (
                            <MarkdownTable
                                markdownContent={v.insert}
                                showRotateButton={showRotateButton}
                                tableTitle={tableTitle}
                                showExpandButton={true}
                            />
                        );
                    }
                    return (
                        <MemoizedMarkdownInner key={v.insert}>
                            {v.insert}
                        </MemoizedMarkdownInner>
                    );
                })}
            </>
        );
    },
    (prev, next) =>
        prev.children.length === next.children.length &&
        prev.tableTitle === next.tableTitle,
);

export default MemoizedMarkdown;
