import { Platform } from '@mrn/react-native';

const INJECTED_JAVASCRIPT = `
(function () {

    // 上报高度变化的函数
    const reportHeight = () => {
        window.MRNWebView.postMessage(
            JSON.stringify({
                type: 'heightChangedInjected',
                payload: document.getElementById('root')?.scrollHeight || document.body.scrollHeight,
            }),
        );
    };

    setInterval(() => {
        reportHeight();
    }, 200);

    // 监控到点击事件后则轮询body的scrollHeight，因为点击事件可能造成scrollHeight变化
    window.onload = () => {
        // 初始上报高度
        reportHeight();

        // 尝试为已存在的root元素添加监听器
        const rootElement = document.getElementById('root');
        if (rootElement) {
            attachRootObservers(rootElement);
        }

        // 开始监控DOM变化，检测root元素的新增
        observeRootElementAddition();
    };

    // 处理安卓和ios差异，安卓需使用document.addEventListener，ios需使用window.addEventListener
    ${Platform.select({
        ios: '',
        android:
            'window.addEventListener = (...p) => document.addEventListener(...p);',
    })}
    true;
})();
  `;

export default INJECTED_JAVASCRIPT;
