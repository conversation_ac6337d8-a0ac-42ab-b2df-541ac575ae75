import KNB from '@mrn/mrn-knb';
import WebView from '@mrn/mrn-webview';
import { NativeSyntheticEvent } from '@mrn/react-native';
import { useRef, useState } from 'react';

const useWebview = (defaultWebviewHeight = 200) => {
    const [webViewHeight, setWebViewHeight] = useState(defaultWebviewHeight);
    const ref = useRef<WebView>(null);
    const [loaded, setLoaded] = useState(false);
    const onMessage = (e: NativeSyntheticEvent<any>) => {
        try {
            const postMessageData: { type: string; payload: any } = e
                .nativeEvent.data
                ? JSON.parse(e.nativeEvent.data)
                : {};
            switch (postMessageData?.type) {
                case 'heightChanged':
                    postMessageData?.payload &&
                        setWebViewHeight(Number(postMessageData?.payload));
                    break;
                case 'ready':
                    ref.current?.postMessage?.(
                        JSON.stringify({ type: 'loadData' }),
                    );
                    ref.current?.postMessage?.(
                        JSON.stringify({ type: 'interval' }),
                    );
                    break;
                case 'loaded':
                    setLoaded(true);
                    break;
                case 'navigate':
                    const url = postMessageData?.payload;
                    if (url) {
                        KNB.openPage({ url });
                    }
                    break;
                default:
                    break;
            }
        } catch (error) {
            console.log(error);
        }
    };
    return {
        webViewHeight,
        onMessage,
        ref,
        loaded,
    };
};
export default useWebview;
