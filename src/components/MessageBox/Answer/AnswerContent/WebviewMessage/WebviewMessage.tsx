import KNB from '@mrn/mrn-knb';
import WebView from '@mrn/mrn-webview';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, PixelRatio, Platform, View } from 'react-native';

import useWebview from './hooks/useWebview';
import { WebviewMessage } from '../../../../../types/message';

import Condition from '@/components/Condition/Condition';

export default ({ data }: { data: WebviewMessage }) => {
    const { ref, webViewHeight, onMessage, loaded } = useWebview();
    const [errorKey, setErrorKey] = useState(0);

    useEffect(() => {
        KNB.subscribe({
            action: 'ready',
            handle: () => {
                KNB.publish({ action: 'loadData' });
                ref.current.postMessage?.(JSON.stringify({ type: 'interval' }));
            },
        });
    }, []);

    const handleError = () => {
        // 当发生错误时，通过改变 key 来重新渲染 WebView
        setErrorKey((prev) => prev + 1);
    };

    return (
        <>
            <Condition condition={[!loaded]}>
                <View
                    style={{
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                        height: 100,
                    }}
                >
                    <ActivityIndicator color={'#222'} />
                </View>
            </Condition>

            <WebView
                ref={ref}
                sharedCookiesEnabled={true}
                onMessage={onMessage}
                javaScriptEnabled={true}
                domStorageEnabled={true}
                scrollEnabled={false}
                showsVerticalScrollIndicator={false}
                originWhitelist={['*']}
                mixedContentMode="always" // 启用混合内容模式
                key={`${data.type}-${data.insert.webview.url}-${errorKey}`}
                source={{ uri: data.insert.webview.url }}
                onError={handleError}
                containerStyle={{
                    height: loaded
                        ? Platform.select({
                              ios: webViewHeight / PixelRatio.get(),
                              android:
                                  (webViewHeight / PixelRatio.get()) * 1.09,
                          })
                        : 1,
                }}
            />
        </>
    );
};
