import { msi } from '@mfe/waimai-mfe-bee-common';
import {
    ActivityIndicator,
    Image,
    StyleSheet,
    TouchableOpacity,
    View,
} from '@mrn/react-native';
import React, { useMemo } from 'react';

import ErrorImg from '../../../assets/images/error.png';
import waveImg from '../../../assets/images/voice/wave.png';
import useMessage from '../../../hooks/useMessage';
import { EntryPointType, Message, MessageStatus } from '../../../types';
import Condition from '../../Condition/Condition';
import TextMessage from '../Answer/AnswerContent/TextMessage';

import TWS from '@/TWS';
import { AdditionMessage } from '@/types/message';

interface SenderMessage {
    data: Message;
}

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        alignItems: 'center',
        paddingLeft: 32,
    },
    text: {
        fontSize: 14,
        color: '#222',
    },
});

const SenderMessage = (props: SenderMessage) => {
    const {
        data: { status },
    } = props;
    const retrySend = useMessage((state) => state.retrySend);

    const [addition, onlyAddition]: [AdditionMessage, boolean] = useMemo(() => {
        try {
            const data = Array.isArray(props.data.currentContent)
                ? props.data.currentContent
                : JSON.parse(props.data.currentContent);
            const additionInner = data.find((v: any) => v.type === 'addition');
            const onlyAdditionInner = data.every(
                (v: any) => v.type === 'addition',
            );
            return [additionInner, onlyAdditionInner];
        } catch (error) {
            return [null, false];
        }
    }, [props.data]);

    const onlyHideSpan: [AdditionMessage, boolean] = useMemo(() => {
        try {
            const data = Array.isArray(props.data.currentContent)
                ? props.data.currentContent
                : JSON.parse(props.data.currentContent);
            const onlyHideSpanInner = data.every(
                (v: any) => v.type === 'hideSpan',
            );
            return onlyHideSpanInner;
        } catch (error) {
            return false;
        }
    }, [props.data]);

    if (onlyHideSpan) {
        return null;
    }

    return (
        <>
            <Condition condition={[!!addition]}>
                <View
                    style={{
                        alignItems: 'flex-end',
                        marginBottom: onlyAddition ? 12 : 4,
                    }}
                >
                    {addition?.insert.addition.additionList.map((v) => {
                        return (
                            <TouchableOpacity
                                style={{
                                    borderRadius: 4,
                                    overflow: 'hidden',
                                    marginRight: 4,
                                }}
                                key={v.src}
                                onPress={() => {
                                    msi.previewImage({
                                        current: v.src,
                                        urls: addition.insert.addition.additionList.map(
                                            (v) => v.src,
                                        ),
                                    });
                                }}
                            >
                                <Image
                                    source={{ uri: v.src }}
                                    style={TWS.square(80)}
                                />
                            </TouchableOpacity>
                        );
                    })}
                </View>
            </Condition>
            <Condition condition={[!onlyAddition]}>
                <View style={{ alignItems: 'flex-end', marginBottom: 12 }}>
                    <View style={styles.container}>
                        {/* 超时则展示加载状态 */}
                        <Condition
                            condition={[status === MessageStatus.GENERATING]}
                        >
                            <ActivityIndicator
                                size={'small'}
                                color={'#222'}
                                style={{
                                    width: 24,
                                    height: 24,
                                    marginRight: 8,
                                }}
                            />
                        </Condition>

                        {/* 错误则展示错误状态，点击支持重发 */}
                        <Condition condition={[status === MessageStatus.ERROR]}>
                            <TouchableOpacity
                                onPress={() => {
                                    retrySend(props.data.msgId);
                                }}
                            >
                                <Image
                                    source={ErrorImg}
                                    style={{
                                        width: 24,
                                        height: 24,
                                        marginRight: 8,
                                    }}
                                />
                            </TouchableOpacity>
                        </Condition>

                        {/* 消息主体 */}
                        <TextMessage
                            data={props.data}
                            shouldTyping={false}
                            style={{
                                borderRadius: 10.5,
                                backgroundColor: '#FFF8CA',
                                paddingHorizontal: 16,
                                paddingVertical: 12,
                            }}
                        />
                    </View>

                    {/* 语音消息的logo */}
                    <Condition
                        condition={[
                            props.data.entryPointType === EntryPointType.VOICE,
                        ]}
                    >
                        <Image
                            source={waveImg}
                            style={{ width: 58, height: 10, marginTop: 6 }}
                        />
                    </Condition>
                </View>
            </Condition>
        </>
    );
};

export default SenderMessage;
