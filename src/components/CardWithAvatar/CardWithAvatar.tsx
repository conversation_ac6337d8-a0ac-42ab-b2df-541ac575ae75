import { View, Text, StyleSheet, Image } from '@mrn/react-native';
import React from 'react';

interface Content {
    label?: string;
    value: string;
    block?: boolean;
}

interface CardWithAvatarProps {
    avatar: string;
    title: string;
    content: Content[];
    actionButton?: React.ReactNode;
}

const CardWithAvatar: React.FC<CardWithAvatarProps> = ({
    avatar,
    title,
    content,
    actionButton,
}) => {
    return (
        <View style={styles.container}>
            <Image
                source={{ uri: avatar }}
                style={styles.avatar}
                defaultSource={require('../../assets/images/defaultImage.png')}
            />
            <View style={styles.contentContainer}>
                <Text
                    style={styles.title}
                    numberOfLines={1}
                    ellipsizeMode="middle"
                >
                    {title}
                </Text>
                <View style={styles.detailsContainer}>
                    {content.map((item, index) => (
                        <View
                            key={index}
                            style={[
                                styles.detailItem,
                                item.block && styles.blockItem,
                            ]}
                        >
                            {item.label && (
                                <Text style={styles.label}>{item.label}:</Text>
                            )}
                            <Text style={styles.value}>{item.value}</Text>
                        </View>
                    ))}
                </View>
            </View>
            {actionButton && (
                <View style={styles.actionContainer}>{actionButton}</View>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        width: '100%',
    },
    avatar: {
        width: 48,
        height: 48,
        borderRadius: 8,
        marginRight: 9,
    },
    contentContainer: {
        flex: 1,
    },
    title: {
        fontSize: 16,
        fontWeight: '500',
        color: '#222',
        marginBottom: 5,
    },
    detailsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
    },
    detailItem: {
        flexDirection: 'row',
        marginRight: 9,
        alignItems: 'center',
    },
    blockItem: {
        width: '100%',
        marginBottom: 2,
    },
    label: {
        fontSize: 16,
        color: '#666',
        marginRight: 8,
    },
    value: {
        fontSize: 16,
        color: '#666',
    },
    actionContainer: {
        marginLeft: 8,
    },
});

export default CardWithAvatar;
