import { Toast } from '@roo/roo-rn';
import { useState, useCallback } from 'react';

import { useSendMessage } from './useSendMessage';
import { EntryPointType, EntryPoint } from '../types';

interface AICallParams {
    taskName?: string;
    callScript?: string;
    targetCount?: number;
    [key: string]: any;
}

interface UseAICallModalReturn {
    visible: boolean;
    openModal: (params?: AICallParams) => void;
    closeModal: () => void;
    submitTask: (taskData: any) => Promise<void>;
    initialParams: AICallParams;
}

export const useAICallModal = (): UseAICallModalReturn => {
    const [visible, setVisible] = useState(false);
    const [initialParams, setInitialParams] = useState<AICallParams>({});
    const { send } = useSendMessage();

    const openModal = useCallback((params: AICallParams = {}) => {
        setInitialParams(params);
        setVisible(true);
    }, []);

    const closeModal = useCallback(() => {
        setVisible(false);
        setInitialParams({});
    }, []);

    const submitTask = useCallback(
        async (taskData: any) => {
            try {
                console.log('外呼任务创建成功，发送hideSpan消息:', taskData);

                // 发送hideSpan类型消息给后端
                // RN端的send函数接受参数：(content, entryPointType, entryPoint)
                const hideSpanContent = JSON.stringify([
                    {
                        type: 'hideSpan',
                        insert: JSON.stringify([taskData]),
                    },
                ]);

                send(
                    hideSpanContent,
                    EntryPointType.TOOL,
                    EntryPoint.action_card,
                );

                Toast.open('外呼任务创建成功');
            } catch (error) {
                console.error('发送外呼任务数据失败:', error);
                Toast.open('发送任务数据失败，请重试');
                throw error;
            }
        },
        [send],
    );

    return {
        visible,
        openModal,
        closeModal,
        submitTask,
        initialParams,
    };
};
