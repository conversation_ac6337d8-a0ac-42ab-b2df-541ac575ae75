import { NativeModules, findNodeHandle } from '@mrn/react-native';
import { DependencyList, useEffect, useRef, useState } from 'react';

export interface Layout {
    pageY: number;
    pageX: number;
    x: number;
    y: number;
    width: number;
    height: number;
    offsetY: number;
    offsetX: number;
}

export const useLayout = (config?: {
    deps?: DependencyList;
    delay?: number;
}) => {
    const { deps = [], delay = 500 } = config || {};
    const [layout, setLayout] = useState<Layout>({
        x: null,
        y: null,
        width: null,
        height: null,
        pageX: null,
        pageY: null,
        offsetY: null,
        offsetX: null,
    });
    const dom = useRef<any>();

    useEffect(() => {
        if (!dom.current) {
            return;
        }
        onLayout({ target: dom.current });
    }, deps);

    const onLayout = (e) => {
        // slideModal的offset是head区域距离顶部高度pageY加上其自身高度height
        const target = e.target;
        dom.current = e.target;
        const handle = findNodeHandle(target);
        setTimeout(() => {
            NativeModules.UIManager.measure(
                handle,
                (x, y, width, height, pageX, pageY) => {
                    setLayout({
                        x,
                        y,
                        width,
                        height,
                        pageX,
                        pageY,
                        offsetY: pageY + height,
                        offsetX: pageX + width,
                    });
                },
            );
        }, delay);
    };

    const reLayout = () => {
        onLayout({ target: dom.current });
    };
    return {
        layout,
        onLayout,
        reLayout,
    };
};
