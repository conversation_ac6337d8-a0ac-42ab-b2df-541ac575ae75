import { msi } from '@mfe/waimai-mfe-bee-common';
import useSWR from 'swr';

const MSI_STORAGE_KEY = 'bee-assistant';
const STORAGE_SWR_KEY = 'storage';
const FALLBACK_DATA = {
    notificationBadge: {
        tools: ['相册', '拍照'],
    },
    isMockMode: false,
};

const useBeeAssistantStorage = () => {
    const updateMSIStorage = (draftStorage: any) =>
        new Promise((resolve, reject) => {
            msi.setStorage({
                key: MSI_STORAGE_KEY,
                data: JSON.stringify(draftStorage),
                success: (res) => {
                    resolve(res);
                },
                fail: (err) => {
                    reject(err);
                },
            });
        });
    const { data: storage, mutate } = useSWR<Record<string, any>>(
        STORAGE_SWR_KEY,
        async () => {
            return new Promise((resolve, reject) => {
                msi.getStorage({
                    key: MSI_STORAGE_KEY,
                    success: (res) => {
                        if (!res?.data) {
                            updateMSIStorage(FALLBACK_DATA);
                            resolve(FALLBACK_DATA);
                        }
                        try {
                            resolve(JSON.parse(res.data));
                        } catch (error) {
                            reject(error);
                        }
                    },
                    fail: (err) => {
                        // key参数不存在
                        if (err.errno === 29999) {
                            updateMSIStorage(FALLBACK_DATA);
                        }
                        reject(err);
                    },
                });
            });
        },
    );

    const updateStorage = (newStorage: any) => {
        mutate(
            async (pre) => {
                const draftStorage =
                    typeof newStorage === 'function'
                        ? newStorage(pre)
                        : newStorage;
                try {
                    await updateMSIStorage(draftStorage);
                } catch {
                    return pre;
                }
                return draftStorage;
            },
            {
                populateCache: true,
                revalidate: false,
            },
        );
    };

    return { storage, updateStorage };
};

const useStorage = (key: keyof typeof FALLBACK_DATA) => {
    const { storage, updateStorage } = useBeeAssistantStorage();

    return [
        storage?.[key],
        (value: any) =>
            updateStorage((pre) => {
                return { ...(pre || {}), [key]: value };
            }),
    ];
};

export default useStorage;
