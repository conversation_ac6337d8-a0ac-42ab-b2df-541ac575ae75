import _ from 'lodash';
import { useEffect, useRef } from 'react';

import { mockServiceData } from './mockData';
import { useUiState } from '../../store/uiState';
import {
    MessageType,
    MessageStatus,
    MessageContentType,
    AbilityType,
} from '../../types';
import useMessage from '../useMessage';
import useStorage from '../useStorage';

const useMockMode = () => {
    const [isMockMode, setIsMockMode] = useStorage('isMockMode');
    const prevMockModeRef = useRef(isMockMode);

    // 使用React Native项目的状态管理
    const add = useMessage((state) => state.add);
    const reset = useMessage((state) => state.reset);
    const setShowHome = useUiState((state) => state.setShowHome);

    // 发送待签入商家列表
    const renderMockData = async () => {
        if (!__DEV__) {
            return;
        }

        // 隐藏首页
        setShowHome(false);

        // 添加用户问题消息
        const questionMsgId = _.uniqueId('mock_question_');
        add({
            msgId: questionMsgId,
            type: MessageType.QUESTION,
            status: MessageStatus.DONE,
            msgType: MessageContentType.TEXT,
            currentContent: '这是mock发出消息',
            abilityType: AbilityType.GENERAL,
            subAbilityType: null,
            isQuestion: true,
        });

        // 延迟一下再显示回答
        setTimeout(() => {
            try {
                // 使用待签入商家列表的mock数据
                const mockData = mockServiceData.toSignPoiList;
                console.log('Mock模式：渲染待签入商家列表', mockData);

                // 提取实际的响应数据
                const responseData = mockData.data;

                // 添加AI回答消息
                add({
                    msgId: responseData.msgId || _.uniqueId('mock_answer_'),
                    questionMsgId: questionMsgId,
                    type: responseData.type || MessageType.ANSWER,
                    status: responseData.status || MessageStatus.DONE,
                    msgType: responseData.msgType || MessageContentType.TEXT,
                    abilityType:
                        responseData.abilityType || AbilityType.GENERAL,
                    subAbilityType: responseData.subAbilityType || null,
                    currentContent:
                        typeof responseData.currentContent === 'string'
                            ? responseData.currentContent
                            : JSON.stringify(responseData.currentContent),
                    previousContent: responseData.previousContent || null,
                    prefixTextContent: responseData.prefixTextContent || null,
                    postTextContent: responseData.postTextContent || null,
                    selectionItems: responseData.selectionItems || null,
                    imageList: responseData.imageList || null,
                    feedbackType: responseData.feedbackType || null,
                    sensitive: responseData.sensitive || false,
                    hasNext: responseData.hasNext || false,
                    pageNum: responseData.pageNum || null,
                    respTime: responseData.respTime || Date.now(),
                    tags: responseData.tags || null,
                });
            } catch (error: any) {
                console.error('Mock模式：渲染待签入商家列表失败', error);
                // 添加错误消息
                add({
                    msgId: _.uniqueId('mock_error_'),
                    type: MessageType.ANSWER,
                    status: MessageStatus.ERROR,
                    msgType: MessageContentType.TEXT,
                    currentContent: 'Mock数据渲染失败: ' + error.message,
                    abilityType: AbilityType.GENERAL,
                    subAbilityType: null,
                    sensitive: false,
                    hasNext: false,
                    respTime: Date.now(),
                });
            }
        }, 500);
    };

    // 渲染mock数据
    const renderMockData2 = async () => {
        // 非localhost环境 直接跳过
        if (!__DEV__) {
            return;
        }
        // 隐藏首页
        setShowHome(false);
        const mockData = mockServiceData.aiCallCompleteFlow as any;

        // 不需要重新生成sessionId，使用useStartChat创建的sessionId

        // 添加用户问题消息 - 使用正确的格式
        const questionMsgId = _.uniqueId('mock_question_');
        add({
            msgId: questionMsgId,
            type: MessageType.QUESTION,
            status: MessageStatus.DONE,
            msgType: MessageContentType.TEXT,
            currentContent: '这是mock发出消息',
            abilityType: AbilityType.GENERAL,
            subAbilityType: null,
            isQuestion: true,
        });

        // 延迟一下再显示回答
        setTimeout(() => {
            try {
                // 使用传入的mockData或默认数据
                const sourceData = mockData;
                console.log('Mock模式：渲染内容', sourceData);

                // 提取实际的响应数据，参考fetchAnswer接口的处理逻辑
                const responseData = sourceData.data || sourceData;

                // 构建符合Message接口的消息对象，参考fetchAnswer接口返回的数据结构
                const mockMessage = {
                    msgId: responseData.msgId || _.uniqueId('mock_answer_'),
                    questionMsgId: questionMsgId, // 关联到问题消息
                    type: responseData.type || MessageType.ANSWER,
                    status: responseData.status || MessageStatus.DONE,
                    msgType: responseData.msgType || MessageContentType.TEXT,
                    abilityType:
                        responseData.abilityType || AbilityType.GENERAL,
                    subAbilityType: responseData.subAbilityType || null,
                    // 确保currentContent是正确的格式 - 如果是字符串则直接使用，如果是对象则转换
                    currentContent:
                        typeof responseData.currentContent === 'string'
                            ? responseData.currentContent
                            : JSON.stringify(responseData.currentContent),
                    previousContent: responseData.previousContent || null,
                    prefixTextContent: responseData.prefixTextContent || null,
                    postTextContent: responseData.postTextContent || null,
                    selectionItems: responseData.selectionItems || null,
                    imageList: responseData.imageList || null,
                    feedbackType: responseData.feedbackType || null,
                    sensitive: responseData.sensitive || false,
                    hasNext: responseData.hasNext || false,
                    pageNum: responseData.pageNum || null,
                    respTime: responseData.respTime || Date.now(),
                    tags: responseData.tags || null,
                };

                // 添加AI回答消息，使用与fetchAnswer接口相同的数据结构
                add(mockMessage);
            } catch (error: any) {
                console.error('Mock模式：渲染数据失败', error);
                // 添加错误消息
                add({
                    msgId: _.uniqueId('mock_error_'),
                    type: MessageType.ANSWER,
                    status: MessageStatus.ERROR,
                    msgType: MessageContentType.TEXT,
                    currentContent: 'Mock数据渲染失败: ' + error.message,
                    abilityType: AbilityType.GENERAL,
                    subAbilityType: null,
                    sensitive: false,
                    hasNext: false,
                    respTime: Date.now(),
                });
            }
        }, 150);
    };
    console.log('renderMockData2', renderMockData2);

    // 处理Mock模式切换为false的响应
    const handleMockModeDisabled = () => {
        console.log('Mock模式已关闭，清空消息并显示首页');
        // 清空当前消息列表
        reset();
        // 显示首页
        setShowHome(true);
    };

    // 监听Mock模式状态变化和sessionId变化
    useEffect(() => {
        const isDevMock = __DEV__ && isMockMode;
        const prevMockMode = prevMockModeRef.current;

        // 检测Mock模式从true变为false
        if (__DEV__ && prevMockMode && !isMockMode) {
            handleMockModeDisabled();
        }

        if (isDevMock) {
            setTimeout(() => {
                renderMockData();
            }, 500);
        } else if (!isDevMock) {
            setIsMockMode(false);
        }

        // 更新前一次的状态
        prevMockModeRef.current = isMockMode;
    }, [isMockMode]);

    // 启用Mock模式
    const enableMockMode = () => {
        if (__DEV__) {
            setIsMockMode(true);
        }
    };

    // 禁用Mock模式
    const disableMockMode = () => {
        setIsMockMode(false);
    };

    const refreshMock = () => {
        renderMockData();
    };

    return {
        // 重要：一定是仅在yarn start的dev环境下才有能力开启mock模式
        isMockMode: __DEV__ && isMockMode,
        enableMockMode,
        disableMockMode,
        refreshMock,
    };
};

export default useMockMode;
