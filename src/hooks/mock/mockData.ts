export const mockServiceData = {
    // 排序表格
    sortTable: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '87027',
            msgId: '87031',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent:
                '[{"type":"markdown","insert":{"markdown":{"text":"### 新签攻克绩效综述：\\n- BD名称：余海龙\\n- 月新签模块得分：18.75%\\n- 目标新商家运营绩效得分：100%\\n- 普通新签目标完成率：18.75%（3 / 16）\\n\\n如果将以下 **_5_** 个商家全部攻克下来，您将预计提升 **_34.25%_** 绩效分（数据仅供参考，实际绩效以最终结果为准），其中上月追溯未攻克可以提高 **_14.00%_** 分，本月未攻克可以提高 **_20.25%_** 分。\\n\\n### 待攻克商家明细：\\n\\n#### 上月追溯未攻克商家推荐：\\n| 商家ID | 商家名称 | 新签追溯商家截止时间 | 剩余攻克时间 | 商家线索等级 | 上月追溯未攻克原因 | 距离攻克差值 | 攻克后质量得分 |\\n|--------|----------|----------------------|--------------|--------------|-------------------|--------------|----------------|\\n| [28137313](https://igate.waimai.meituan.com/igate/shangdan/shopBasic/newShopBasic.html?wmPoiId=28137313) | 忆湘阁（虹桥店） | 20250708 | 1 | 1.0 | 月累计在线订单数 | 在线订单数差值4单 | 0.00% |\\n| [28446791](https://igate.waimai.meituan.com/igate/shangdan/shopBasic/newShopBasic.html?wmPoiId=28446791) | 开心派对（简餐） | 20250724 | 17 | 2.0 | 月累计单均价（实付）≥15元 | 月均单均价差值1.11元 | 1.50% |\\n\\n#### 本月未攻克商家推荐：\\n| 商家ID | 商家名称 | 商家线索等级 | 当月未攻克原因 | 距离攻克差值 | 攻克后质量得分 |\\n|--------|----------|--------------|----------------|--------------|----------------|\\n| [27659465](https://igate.waimai.meituan.com/igate/shangdan/shopBasic/newShopBasic.html?wmPoiId=27659465) | 三胖烧鸡 | 2.0 | 在线订单数低于标准 | 在线订单数差值1单 | 1.50% |\\n| [28586664](https://igate.waimai.meituan.com/igate/shangdan/shopBasic/newShopBasic.html?wmPoiId=28586664) | 阿杰面馆(虹港北路店) | 1.0 | 单均&订单数 | 在线订单数差值8单<br>月均单均价差值1.69元 | 0.00% |\\n| [28663062](https://igate.waimai.meituan.com/igate/shangdan/shopBasic/newShopBasic.html?wmPoiId=28663062) | 百丰面馆 | 1.0 | 营业时长&单均&订单数 | 在线订单数差值10单<br>月均单均价差值15.00元<br>商家营业时长差值24.53小时 | 0.00% |"}}},{"type":"markdown","insert":{"markdown":{"text":"\\n\\n\\n"}}},{"type":"markdown","insert":{"markdown":{"text":""}}},{"type":"markdown","insert":{"markdown":{"text":""}}},{"type":"markdown","insert":{"markdown":{"text":""}}},{"type":"actionCard","insert":{"actionCard":{"button":{"text":"去查询","action":"submitQuestion","type":"primary","question":"批量查询新店加权"},"title":"查新店加权","subTitle":"提升新商家曝光量和下单转化"}}},{"type":"suffixOptions","insert":{"suffixOptions":{"descriptions":"你还想问什么","options":[{"abilityType":1,"operationType":2,"content":"查商家是否开启新店加权"},{"abilityType":1,"operationType":2,"content":"诊断商家经营情况"},{"abilityType":1,"operationType":2,"content":"重新建店如何计算新签攻克绩效？"}]}}}]',
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1751875418321,
            tags: null,
        },
    },
    // 学城图片
    wikiPic: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5121310',
            msgId: '5121314',
            type: 2,
            abilityType: 1,
            status: 0,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请口为城市人员权限申请口为城市人员权限申请口为城市人员权限申请口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为城市人员权限申请入口为：**先富系统-管理工具',
                        },
                    },
                },
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '-城市人员权限申请**，或直接访问 [权限申请入口](',
                        },
                    },
                },
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: 'https://igate.waimai.meituan.com/mfepro/organization/ApplyPermission/ApplyPermission',
                        },
                    },
                },
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '.html#/)。如页面提示无权限，需通过此入口申请。更多申请',
                        },
                    },
                },
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '规范可参考 [城市人员权限申请规范](',
                        },
                    },
                },
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: 'https://km.sankuai.com/page/164327767)。\n ![图片](https://km.sankuai.com/api/file/cdn/1349766872/3513148429xx?contentType=1&isNewContent=false&isNewContent=fals) \n![图片](https://s3plus.sankuai.com/bee-community/tmp_mrn_tmp_cda7eac8d8eb494b78eecaa090ed0e68.jpeg)\n ![图片](https://km.sankuai.com/api/file/cdn/1349766872/3513148429xxx?contentType=1&isNewContent=false&isNewContent=false)\n \n',
                            // "text": "https://km.sankuai.com/page/164327767)。\n \n![图片](https://msstest.sankuai.com/bdaiassistant-public/wiki_picture_4299630522_4487673508.png?AWSAccessKeyId=SRV_SKlNHJY4gJxvSlIIe3K6T2UwE4YYxI3h&Expires=1754645191&Signature=7qQJeASUdH5B2382dIULq%2FShUlo%3D\)\n \n"
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752052430689,
            tags: ['poiId'],
        },
    },
    // FormCard 功能测试数据
    formCardBasic: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '90001',
            msgId: '90002',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'title',
                    insert: {
                        title: {
                            title: 'FormCard基础功能测试',
                            subTitle: '测试输入框和单选按钮',
                        },
                    },
                },
                {
                    type: 'form',
                    insert: {
                        form: {
                            config: [
                                {
                                    label: 'BDmis账号',
                                    type: 'input',
                                    defaultValue: '',
                                    placeholder: '请输入您的BDmis账号',
                                    required: true,
                                },
                                {
                                    label: '绩效目标',
                                    type: 'radio',
                                    options: ['100%', '120%', '150%'],
                                    defaultValue: '100%',
                                },
                            ],
                            buttonText: '提交基础信息',
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1751875418321,
            tags: null,
        },
    },
    // 多行文本输入框测试
    formCardTextarea: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '90003',
            msgId: '90004',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'title',
                    insert: {
                        title: {
                            title: '多行文本输入测试',
                            subTitle: '支持多行文本输入和长度校验',
                        },
                    },
                },
                {
                    type: 'form',
                    insert: {
                        form: {
                            config: [
                                {
                                    label: '问题描述',
                                    type: 'textarea',
                                    numberOfLines: 4,
                                    placeholder:
                                        '请详细描述您遇到的问题，包括具体情况、错误信息等',
                                    minLength: 10,
                                    maxLength: 500,
                                    required: true,
                                },
                                {
                                    label: '联系方式',
                                    type: 'input',
                                    placeholder: '请输入手机号或企业微信',
                                    minLength: 6,
                                    maxLength: 20,
                                },
                            ],
                            buttonText: '提交问题反馈',
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1751875418321,
            tags: null,
        },
    },
    // Select下拉选择测试
    formCardSelect: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '90005',
            msgId: '90006',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'title',
                    insert: {
                        title: {
                            title: 'ActionSheet选择测试',
                            subTitle: '使用ActionSheet实现更友好的选择体验',
                        },
                    },
                },
                {
                    type: 'form',
                    insert: {
                        form: {
                            config: [
                                {
                                    label: '城市选择',
                                    type: 'select',
                                    options: [
                                        '北京',
                                        '上海',
                                        '广州',
                                        '深圳',
                                        '杭州',
                                        '成都',
                                        '武汉',
                                        '西安',
                                    ],
                                    placeholder: '请选择您所在的城市',
                                    required: true,
                                },
                                {
                                    label: '业务类型',
                                    type: 'select',
                                    options: [
                                        '外卖',
                                        '到店',
                                        '酒旅',
                                        '出行',
                                        '金融',
                                    ],
                                    defaultValue: '外卖',
                                },
                                {
                                    label: '紧急程度',
                                    type: 'radio',
                                    options: ['一般', '紧急', '非常紧急'],
                                    defaultValue: '一般',
                                },
                            ],
                            buttonText: '确认选择',
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1751875418321,
            tags: null,
        },
    },
    // 长度校验测试
    formCardValidation: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '90007',
            msgId: '90008',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'title',
                    insert: {
                        title: {
                            title: '校验功能测试',
                            subTitle: '测试长度校验、正则校验和必填校验',
                        },
                    },
                },
                {
                    type: 'form',
                    insert: {
                        form: {
                            config: [
                                {
                                    label: '用户名',
                                    type: 'input',
                                    placeholder:
                                        '3-20个字符，支持字母数字下划线',
                                    minLength: 3,
                                    maxLength: 20,
                                    regExp: '^[a-zA-Z0-9_]+$',
                                    message:
                                        '用户名格式不正确，只能包含字母、数字和下划线',
                                    required: true,
                                },
                                {
                                    label: '手机号',
                                    type: 'input',
                                    placeholder: '请输入11位手机号',
                                    regExp: '^1[3-9]\\d{9}$',
                                    message: '请输入正确的手机号格式',
                                    required: true,
                                },
                                {
                                    label: '邮箱',
                                    type: 'input',
                                    placeholder: '请输入邮箱地址',
                                    regExp: '^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$',
                                    message: '请输入正确的邮箱格式',
                                    required: false,
                                },
                                {
                                    label: '个人简介',
                                    type: 'textarea',
                                    numberOfLines: 3,
                                    placeholder: '请简单介绍一下自己（可选）',
                                    maxLength: 200,
                                    required: false,
                                },
                            ],
                            buttonText: '验证并提交',
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1751875418321,
            tags: null,
        },
    },
    // hideSpan和自定义提交测试
    formCardHideSpan: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '90009',
            msgId: '90010',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'title',
                    insert: {
                        title: {
                            title: 'HideSpan提交方式测试',
                            subTitle: '隐藏提交按钮，通过其他方式触发提交',
                        },
                    },
                },
                {
                    type: 'form',
                    insert: {
                        form: {
                            config: [
                                {
                                    label: '快速搜索',
                                    type: 'input',
                                    placeholder:
                                        '输入内容后自动搜索，无需点击按钮',
                                    maxLength: 50,
                                },
                                {
                                    label: '搜索类型',
                                    type: 'select',
                                    options: [
                                        '商家名称',
                                        '商家ID',
                                        '联系人',
                                        '地址',
                                    ],
                                    defaultValue: '商家名称',
                                },
                            ],
                            hideSpan: true,
                        },
                    },
                },
                {
                    type: 'text',
                    insert: '提示：此表单隐藏了提交按钮，输入完成后会自动处理',
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1751875418321,
            tags: null,
        },
    },
    // 综合功能测试
    formCardComprehensive: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '90011',
            msgId: '90012',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'title',
                    insert: {
                        title: {
                            title: 'FormCard综合功能测试',
                            subTitle: '包含所有新功能的完整表单',
                        },
                    },
                },
                {
                    type: 'form',
                    insert: {
                        form: {
                            config: [
                                {
                                    label: '姓名',
                                    type: 'input',
                                    placeholder: '请输入真实姓名',
                                    minLength: 2,
                                    maxLength: 10,
                                    required: true,
                                    tooltip: '请输入您的真实姓名，用于身份确认',
                                },
                                {
                                    label: '部门',
                                    type: 'select',
                                    options: [
                                        '技术部',
                                        '产品部',
                                        '运营部',
                                        '市场部',
                                        '销售部',
                                        '人事部',
                                        '财务部',
                                    ],
                                    placeholder: '请选择您的部门',
                                    required: true,
                                    tooltip: '选择您当前所在的部门',
                                },
                                {
                                    label: '职级',
                                    type: 'radio',
                                    options: [
                                        '初级',
                                        '中级',
                                        '高级',
                                        '专家',
                                        '管理',
                                    ],
                                    defaultValue: '中级',
                                    tooltip: '选择您当前的职级',
                                },
                                {
                                    label: '联系电话',
                                    type: 'input',
                                    placeholder: '请输入11位手机号',
                                    regExp: '^1[3-9]\\d{9}$',
                                    message: '请输入正确的手机号格式',
                                    required: true,
                                },
                                {
                                    label: '工作经历',
                                    type: 'textarea',
                                    numberOfLines: 5,
                                    placeholder: '请简述您的工作经历和主要成就',
                                    minLength: 20,
                                    maxLength: 1000,
                                    required: true,
                                    tooltip: '详细描述有助于更好地了解您的背景',
                                },
                                {
                                    label: '兴趣爱好',
                                    type: 'textarea',
                                    numberOfLines: 3,
                                    placeholder: '分享一下您的兴趣爱好（可选）',
                                    maxLength: 300,
                                    required: false,
                                },
                                {
                                    label: '工作地点偏好',
                                    type: 'select',
                                    options: [
                                        '北京',
                                        '上海',
                                        '广州',
                                        '深圳',
                                        '杭州',
                                        '成都',
                                        '远程办公',
                                        '其他',
                                    ],
                                    placeholder: '选择您希望的工作地点',
                                },
                                {
                                    label: '是否接受出差',
                                    type: 'radio',
                                    options: [
                                        '经常出差',
                                        '偶尔出差',
                                        '不接受出差',
                                    ],
                                    defaultValue: '偶尔出差',
                                },
                            ],
                            title: '员工信息登记表',
                            subTitle:
                                '请完整填写以下信息，所有必填项都需要填写',
                            buttonText: '提交登记信息',
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1751875418321,
            tags: null,
        },
    },
    // MarkdownWithStyle 组件测试
    markdownWithStyle: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '95001',
            msgId: '95002',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'text',
                    insert: '以下是不同样式的Markdown内容展示：\n\n',
                },
                {
                    type: 'markdownWithStyle',
                    insert: {
                        markdownWithStyle: {
                            markdown:
                                '⚠️ **重要警告**\n\n系统将在今晚 **23:00-01:00** 进行维护，请提前保存您的工作。\n\n- 维护期间无法访问系统\n- 请及时备份重要数据\n- 如有紧急情况请联系技术支持',
                            background: '#FFF3CD',
                        },
                    },
                },
                {
                    type: 'text',
                    insert: '\n',
                },
                {
                    type: 'markdownWithStyle',
                    insert: {
                        markdownWithStyle: {
                            markdown:
                                '❌ **错误提示**\n\n操作失败，请检查以下项目：\n\n1. 网络连接是否正常\n2. 权限设置是否正确\n3. 输入参数是否有效',
                            background: '#F8D7DA',
                        },
                    },
                },
                {
                    type: 'text',
                    insert: '\n',
                },
                {
                    type: 'markdownWithStyle',
                    insert: {
                        markdownWithStyle: {
                            markdown:
                                '✅ **操作成功**\n\n您的请求已成功处理！\n\n- 数据已保存\n- 邮件通知已发送\n- 日志已记录',
                            background: '#D4EDDA',
                        },
                    },
                },
                {
                    type: 'text',
                    insert: '\n',
                },
                {
                    type: 'markdownWithStyle',
                    insert: {
                        markdownWithStyle: {
                            markdown:
                                'ℹ️ **信息提示**\n\n这是一条包含 [链接](https://www.meituan.com) 和 **粗体文字** 的信息。\n\n支持完整的Markdown语法：\n\n```javascript\nconst message = "Hello World";\nconsole.log(message);\n```',
                            background: '#D1ECF1',
                        },
                    },
                },
                {
                    type: 'text',
                    insert: '\n',
                },
                {
                    type: 'markdownWithStyle',
                    insert: {
                        markdownWithStyle: {
                            markdown:
                                '📊 **数据统计**\n\n| 项目 | 数量 | 状态 |\n|------|------|------|\n| 用户数 | 1,234 | 正常 |\n| 订单数 | 5,678 | 增长 |\n| 收入 | ¥12,345 | 稳定 |',
                            background: '#F8F9FA',
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: Date.now(),
            tags: null,
        },
    },

    // TtButton 按钮组件测试
    ttButton: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '97001',
            msgId: '97002',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'text',
                    insert: '以下是一些常用的快捷操作按钮：\n\n',
                },
                {
                    type: 'ttButton',
                    insert: {
                        ttButton: {
                            buttonText: '查看商家信息',
                            question: '请帮我查看商家的基本信息',
                        },
                    },
                },
                {
                    type: 'text',
                    insert: '\n',
                },
                {
                    type: 'ttButton',
                    insert: {
                        ttButton: {
                            buttonText: '分析营业数据',
                            question: '请分析一下最近的营业数据情况',
                        },
                    },
                },
                {
                    type: 'text',
                    insert: '\n',
                },
                {
                    type: 'ttButton',
                    insert: {
                        ttButton: {
                            buttonText: '优化建议',
                            question: '请给出一些经营优化建议',
                        },
                    },
                },
                {
                    type: 'text',
                    insert: '\n',
                },
                {
                    type: 'markdownWithStyle',
                    insert: {
                        markdownWithStyle: {
                            markdown:
                                'ℹ️ **提示**\n\n点击上方按钮可以快速发送对应的问题，在历史消息中按钮会被禁用。',
                            background: '#D1ECF1',
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: Date.now(),
            tags: null,
        },
    },

    // RecommendedQuestions 推荐问题组件测试
    recommendedQuestions: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '98001',
            msgId: '98002',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'text',
                    insert: '我是您的AI助手，可以帮您解决各种问题。以下是一些常见的问题，您可以直接点击：\n\n',
                },
                {
                    type: 'recommendedQuestions',
                    insert: {
                        recommendedQuestions: {
                            questions: [
                                '如何提升店铺评分？',
                                '怎样增加订单量？',
                                '如何优化菜品搭配？',
                                '营业数据分析',
                                '成本控制建议',
                                '客户投诉处理',
                            ],
                        },
                    },
                },
                {
                    type: 'text',
                    insert: '\n您也可以直接输入其他问题，我会尽力为您解答。',
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: Date.now(),
            tags: null,
        },
    },

    // 混合组件测试
    mixedComponents: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '99001',
            msgId: '99002',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'markdownWithStyle',
                    insert: {
                        markdownWithStyle: {
                            markdown:
                                '🎉 **欢迎使用AI助手**\n\n我可以为您提供多种服务，请选择您需要的功能：',
                            background: '#E6F7FF',
                        },
                    },
                },
                {
                    type: 'ttButton',
                    insert: {
                        ttButton: {
                            buttonText: '快速诊断',
                            question: '请帮我快速诊断店铺问题',
                        },
                    },
                },
                {
                    type: 'ttButton',
                    insert: {
                        ttButton: {
                            buttonText: '数据分析',
                            question: '请分析我的经营数据',
                        },
                    },
                },
                {
                    type: 'text',
                    insert: '\n或者您可以从以下常见问题中选择：\n\n',
                },
                {
                    type: 'recommendedQuestions',
                    insert: {
                        recommendedQuestions: {
                            questions: [
                                '今日营业情况如何？',
                                '本周数据对比',
                                '客户满意度分析',
                                '竞品对比分析',
                                '营销活动建议',
                            ],
                        },
                    },
                },
                {
                    type: 'markdownWithStyle',
                    insert: {
                        markdownWithStyle: {
                            markdown:
                                'ℹ️ **提示**：点击上方按钮或问题可以快速开始对话，历史消息中的交互元素会被禁用。',
                            background: '#F6FFED',
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: Date.now(),
            tags: null,
        },
    },

    // PoiId类型表单测试
    formCardPoiId: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '96001',
            msgId: '96002',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'title',
                    insert: {
                        title: {
                            title: 'PoiId商家选择测试',
                            subTitle: '测试商家选择器功能',
                        },
                    },
                },
                {
                    type: 'markdownWithStyle',
                    insert: {
                        markdownWithStyle: {
                            markdown:
                                'ℹ️ **使用说明**\n\n点击"商家ID"字段可以打开商家选择器，选择商家后会自动回填商家ID。',
                            background: '#D1ECF1',
                        },
                    },
                },
                {
                    type: 'form',
                    insert: {
                        form: {
                            config: [
                                {
                                    label: '商家名称',
                                    type: 'input',
                                    placeholder: '请输入商家名称',
                                    required: true,
                                },
                                {
                                    key: 'poi_id',
                                    label: '商家ID',
                                    type: 'poiId',
                                    placeholder: '点击选择商家',
                                    required: true,
                                },
                                {
                                    label: '联系电话',
                                    type: 'input',
                                    placeholder: '请输入联系电话',
                                    regExp: '^1[3-9]\\d{9}$',
                                    message: '请输入正确的手机号码格式',
                                },
                                {
                                    label: '业务类型',
                                    type: 'select',
                                    options: ['外卖', '到店', '酒旅', '出行'],
                                    defaultValue: '外卖',
                                },
                                {
                                    label: '备注信息',
                                    type: 'textarea',
                                    placeholder: '请输入备注信息（可选）',
                                    maxLength: 200,
                                    numberOfLines: 3,
                                },
                            ],
                            buttonText: '提交商家信息',
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: Date.now(),
            tags: null,
        },
    },

    // JSON类型表单测试
    formCardJson: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '100001',
            msgId: '100002',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            answer: [
                {
                    type: 'title',
                    insert: {
                        title: {
                            title: 'JSON表单测试',
                            subTitle: '测试type为json时发送hideSpan消息',
                        },
                    },
                },
                {
                    type: 'form',
                    insert: {
                        form: {
                            config: [
                                {
                                    label: '用户名',
                                    type: 'input',
                                    placeholder: '请输入用户名',
                                    required: true,
                                },
                                {
                                    label: '邮箱',
                                    type: 'input',
                                    placeholder: '请输入邮箱地址',
                                    required: true,
                                },
                                {
                                    label: '角色',
                                    type: 'select',
                                    options: ['管理员', '编辑者', '查看者'],
                                    placeholder: '请选择角色',
                                    required: true,
                                },
                                {
                                    label: '状态',
                                    type: 'radio',
                                    options: ['启用', '禁用'],
                                    defaultValue: '启用',
                                },
                            ],
                            title: 'JSON数据提交测试',
                            subTitle: '提交时将发送hideSpan类型消息',
                            buttonText: '提交JSON数据',
                            type: 'json',
                            formId: 'user-form-001',
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1751875418321,
            tags: null,
        },
    },

    // ActionCard 单按钮 - 外呼功能
    actionCardSingle: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135624',
            msgId: '5135634',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '基于您的商家数据分析，我们发现以下问题需要关注：',
                        },
                    },
                },
                {
                    type: 'actionCard',
                    insert: {
                        actionCard: {
                            title: '串联次新运营绩效诊断',
                            subTitle:
                                '本月新签商家转化率较低，建议优化运营策略提升商家活跃度',
                            backgroundColor: '#f0f8ff',
                            button: {
                                text: '查看详细报告',
                                action: 'submitQuestion',
                                question:
                                    '请提供详细的串联次新运营绩效分析报告',
                                type: 'primary',
                                color: '#1890ff',
                            },
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // ActionCard 多按钮 - 外呼功能
    actionCardMultiple: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135625',
            msgId: '5135635',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '根据分析结果，为您提供以下操作选项：',
                        },
                    },
                },
                {
                    type: 'actionCard',
                    insert: {
                        actionCard: {
                            title: '商家运营优化建议',
                            subTitle:
                                '基于数据分析，我们为您准备了多种优化方案',
                            backgroundColor: '#fff7e6',
                            buttonList: [
                                {
                                    text: '创建',
                                    action: 'openAICallModal',
                                    AICallParams: {
                                        taskName: '商家运营优化外呼',
                                        callScript:
                                            '您好，我们发现您的店铺运营数据有优化空间，想为您提供一些建议...',
                                        targetCount: 50,
                                    },
                                    type: 'normal',
                                },
                                {
                                    text: '导出数据',
                                    url: 'https://example.com/export-data',
                                    type: 'normal',
                                },
                            ],
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // PieChart 饼图数据
    pieChartData: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135626',
            msgId: '5135636',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '外呼任务执行结果统计如下：',
                        },
                    },
                },
                {
                    type: 'pieChart',
                    insert: {
                        pieChart: {
                            title: '外呼结果统计',
                            data: [
                                {
                                    label: '接通成功',
                                    value: 450,
                                    color: '#FFD100',
                                },
                                {
                                    label: '接通失败',
                                    value: 180,
                                    color: '#FF6B6B',
                                },
                                {
                                    label: '用户拒接',
                                    value: 120,
                                    color: '#FFD93D',
                                },
                                {
                                    label: '号码无效',
                                    value: 50,
                                    color: '#A8A8A8',
                                },
                            ],
                            size: 200,
                            showLegend: true,
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // AICallRecord 外呼任务记录 - 执行中
    AICallRecordRunning: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135627',
            msgId: '5135637',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '您的外呼任务正在执行中，当前进度如下：',
                        },
                    },
                },
                {
                    type: 'AICallRecord',
                    insert: {
                        AICallRecord: {
                            content: [
                                {
                                    jobName: '商家运营优化外呼任务',
                                    status: 'running',
                                    descriptions: [
                                        { label: '外呼商家', value: '1000家' },
                                        {
                                            label: '创建时间',
                                            value: '2024-01-15 10:30:00',
                                        },
                                    ],
                                    button: {
                                        text: '查看详情',
                                        action: 'submitQuestion',
                                        question: '查看外呼任务详细进度',
                                        type: 'normal',
                                    },
                                    createTime: 1705292200000,
                                    progress: 65,
                                },
                            ],
                            extendButtonName: '展开',
                            showNum: 1,
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // AICallRecord 外呼任务记录 - 已完成
    AICallRecordCompleted: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135628',
            msgId: '5135638',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '外呼任务已完成，执行结果如下：',
                        },
                    },
                },
                {
                    type: 'AICallRecord',
                    insert: {
                        AICallRecord: {
                            content: [
                                {
                                    jobName: '新签商家激活外呼',
                                    status: 'success',
                                    descriptions: [
                                        { label: '外呼商家', value: '500家' },
                                        { label: '已完成', value: '500家' },
                                    ],
                                    button: {
                                        text: '查看结果',
                                        action: 'submitQuestion',
                                        question: '查看外呼任务详细结果',
                                        type: 'primary',
                                    },
                                    createTime: 1705206000000,
                                    completeTime: 1705240200000,
                                    progress: 100,
                                },
                                {
                                    jobName: '新签商家激活外呼',
                                    status: 'success',
                                    descriptions: [
                                        { label: '外呼商家', value: '500家' },
                                        { label: '已完成', value: '500家' },
                                    ],
                                    button: {
                                        text: '查看结果',
                                        action: 'submitQuestion',
                                        question: '查看外呼任务详细结果',
                                        type: 'primary',
                                    },
                                    createTime: 1705206000000,
                                    completeTime: 1705240200000,
                                    progress: 100,
                                },
                                {
                                    jobName: '新签商家激活外呼',
                                    status: 'success',
                                    descriptions: [
                                        { label: '外呼商家', value: '500家' },
                                        { label: '已完成', value: '500家' },
                                    ],
                                    button: {
                                        text: '查看结果',
                                        action: 'submitQuestion',
                                        question: '查看外呼任务详细结果',
                                        type: 'primary',
                                    },
                                    createTime: 1705206000000,
                                    completeTime: 1705240200000,
                                    progress: 100,
                                },
                                {
                                    jobName: '新签商家激活外呼',
                                    status: 'success',
                                    descriptions: [
                                        { label: '外呼商家', value: '500家' },
                                        { label: '已完成', value: '500家' },
                                    ],
                                    button: {
                                        text: '查看结果',
                                        action: 'submitQuestion',
                                        question: '查看外呼任务详细结果',
                                        type: 'primary',
                                    },
                                    createTime: 1705206000000,
                                    completeTime: 1705240200000,
                                    progress: 100,
                                },
                            ],
                            extendButtonName: '展开',
                            showNum: 2,
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // 综合场景：外呼任务完整流程
    aiCallCompleteFlow: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135630',
            msgId: '5135640',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: JSON.stringify([
                {
                    type: 'text',
                    insert: '基于您的商家数据分析，我们为您提供完整的外呼解决方案：',
                },
                {
                    type: 'actionCard',
                    insert: {
                        actionCard: {
                            title: '外呼任务管理中心',
                            subTitle: '一站式外呼任务创建、监控和分析平台',
                            backgroundColor: '#f6ffed',
                            buttonList: [
                                {
                                    text: '创建新任务',
                                    action: 'openAICallModal',
                                    AICallParams: {
                                        taskName: '商家激活外呼',
                                        callScript:
                                            '您好，恭喜您成功入驻美团外卖！我们想为您介绍一些运营技巧...',
                                        targetCount: 100,
                                    },
                                    type: 'primary',
                                },
                                {
                                    text: '查看历史',
                                    action: 'submitQuestion',
                                    question: '请显示我的外呼任务历史记录',
                                    type: 'normal',
                                },
                            ],
                        },
                    },
                },
                {
                    type: 'text',
                    insert: '\n最近任务执行情况：',
                },
                {
                    type: 'AICallRecord',
                    insert: {
                        AICallRecord: {
                            content: [
                                {
                                    jobName: '本周商家关怀外呼',
                                    status: 'running',
                                    descriptions: [
                                        { label: '外呼商家', value: '300家' },
                                        { label: '已完成', value: '234家' },
                                        { label: '成功接通', value: '180家' },
                                        { label: '失败', value: '54家' },
                                        {
                                            label: '创建时间',
                                            value: '2024.01.15 08:00:00',
                                        },
                                        {
                                            label: '最近更新',
                                            value: '2024.01.15 15:30:00',
                                        },
                                    ],
                                    button: {
                                        text: '查看进度',
                                        action: 'submitQuestion',
                                        question:
                                            '查看本周商家关怀外呼详细进度',
                                        type: 'normal',
                                    },
                                    createTime: 1705283200000, // 2024-01-15 08:00:00
                                    progress: 78,
                                },
                                {
                                    jobName: '商家激活提醒外呼',
                                    status: 'success',
                                    descriptions: [
                                        { label: '外呼商家', value: '150家' },
                                        { label: '已完成', value: '150家' },
                                        { label: '成功接通', value: '120家' },
                                        {
                                            label: '创建时间',
                                            value: '2024.01.14 16:00:00',
                                        },
                                        {
                                            label: '完成时间',
                                            value: '2024.01.15 10:00:00',
                                        },
                                    ],
                                    button: {
                                        text: '查看结果',
                                        action: 'submitQuestion',
                                        question: '查看商家激活提醒外呼结果',
                                        type: 'primary',
                                    },
                                    createTime: 1705231200000, // 2024-01-14 16:00:00
                                    completeTime: 1705291200000, // 2024-01-15 10:00:00
                                    progress: 100,
                                },
                                {
                                    jobName: '新签商家欢迎外呼',
                                    status: 'init',
                                    descriptions: [
                                        { label: '外呼商家', value: '200家' },
                                        {
                                            label: '计划开始',
                                            value: '2024.01.16 09:00:00',
                                        },
                                        {
                                            label: '创建时间',
                                            value: '2024.01.15 17:00:00',
                                        },
                                    ],
                                    button: {
                                        text: '立即开始',
                                        action: 'submitQuestion',
                                        question:
                                            '立即开始新签商家欢迎外呼任务',
                                        type: 'primary',
                                    },
                                    createTime: 1705316400000, // 2024-01-15 17:00:00
                                },
                            ],
                            redirectButton: {
                                text: '查看结果',
                                action: 'submitQuestion',
                                question: '查看商家激活提醒外呼结果',
                                type: 'primary',
                            },
                            extendButtonName: '查看更多',
                            showNum: 2,
                        },
                    },
                },
                {
                    type: 'text',
                    insert: '\n整体外呼效果分析：',
                },
                {
                    type: 'pieChart',
                    insert: {
                        pieChart: {
                            title: '本月外呼结果汇总',
                            data: [
                                {
                                    label: '成功接通',
                                    value: 1250,
                                    color: '#52c41a',
                                },
                                {
                                    label: '未接听',
                                    value: 680,
                                    color: '#faad14',
                                },
                                {
                                    label: '拒绝接听',
                                    value: 320,
                                    color: '#ff7a45',
                                },
                                {
                                    label: '号码异常',
                                    value: 150,
                                    color: '#d9d9d9',
                                },
                            ],
                            size: 220,
                            showLegend: true,
                        },
                    },
                },
            ]),
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // ActionCard 单按钮 - 外呼功能 (重复)
    actionCardSingleDuplicate: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135624',
            msgId: '5135634',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '基于您的商家数据分析，我们发现以下问题需要关注：',
                        },
                    },
                },
                {
                    type: 'actionCard',
                    insert: {
                        actionCard: {
                            title: '串联次新运营绩效诊断',
                            subTitle:
                                '本月新签商家转化率较低，建议优化运营策略提升商家活跃度',
                            backgroundColor: '#f0f8ff',
                            button: {
                                text: '查看详细报告',
                                action: 'submitQuestion',
                                question:
                                    '请提供详细的串联次新运营绩效分析报告',
                                type: 'primary',
                            },
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // ActionCard 多按钮 - 外呼功能 (重复)
    actionCardMultipleDuplicate: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135625',
            msgId: '5135635',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '根据分析结果，为您提供以下操作选项：',
                        },
                    },
                },
                {
                    type: 'actionCard',
                    insert: {
                        actionCard: {
                            title: '商家运营优化建议',
                            subTitle:
                                '基于数据分析，我们为您准备了多种优化方案',
                            backgroundColor: '#fff7e6',
                            buttonList: [
                                {
                                    text: '创建',
                                    action: 'openAICallModal',
                                    AICallParams: {
                                        taskName: '商家运营优化外呼',
                                        callScript:
                                            '您好，我们发现您的店铺运营数据有优化空间，想为您提供一些建议...',
                                        targetCount: 50,
                                    },
                                    type: 'normal',
                                },
                                {
                                    text: '导出数据',
                                    url: 'https://example.com/export-data',
                                    type: 'normal',
                                },
                            ],
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // PieChart 饼图数据 (重复)
    pieChartDataDuplicate: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135626',
            msgId: '5135636',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '外呼任务执行结果统计如下：',
                        },
                    },
                },
                {
                    type: 'pieChart',
                    insert: {
                        pieChart: {
                            title: '外呼结果统计',
                            data: [
                                {
                                    label: '接通成功',
                                    value: 450,
                                    color: '#6047FA',
                                },
                                {
                                    label: '接通失败',
                                    value: 180,
                                    color: '#FF6B6B',
                                },
                                {
                                    label: '用户拒接',
                                    value: 120,
                                    color: '#FFD93D',
                                },
                                {
                                    label: '号码无效',
                                    value: 50,
                                    color: '#A8A8A8',
                                },
                            ],
                            size: 200,
                            showLegend: true,
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // AICallRecord 外呼任务记录 - 执行中
    aiCallRecordRunning: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135627',
            msgId: '5135637',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '您的外呼任务正在执行中，当前进度如下：',
                        },
                    },
                },
                {
                    type: 'aiCallRecord',
                    insert: {
                        aiCallRecord: {
                            content: [
                                {
                                    jobName: '商家运营优化外呼任务',
                                    status: 'running',
                                    descriptions: [
                                        { label: '外呼商家', value: '1000家' },
                                        {
                                            label: '创建时间',
                                            value: '2024-01-15 10:30:00',
                                        },
                                    ],
                                    button: {
                                        text: '查看详情',
                                        action: 'submitQuestion',
                                        question: '查看外呼任务详细进度',
                                        type: 'normal',
                                    },
                                    createTime: 1705292200000,
                                    progress: 65,
                                },
                            ],
                            extendButtonName: '展开',
                            showNum: 1,
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // AICallRecord 外呼任务记录 - 已完成
    aiCallRecordCompleted: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135628',
            msgId: '5135638',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '外呼任务已完成，执行结果如下：',
                        },
                    },
                },
                {
                    type: 'aiCallRecord',
                    insert: {
                        aiCallRecord: {
                            content: [
                                {
                                    jobName: '新签商家激活外呼',
                                    status: 'success',
                                    descriptions: [
                                        { label: '外呼商家', value: '500家' },
                                        { label: '已完成', value: '500家' },
                                    ],
                                    button: {
                                        text: '查看结果',
                                        action: 'submitQuestion',
                                        question: '查看外呼任务详细结果',
                                        type: 'primary',
                                    },
                                    createTime: 1705206000000,
                                    completeTime: 1705240200000,
                                    progress: 100,
                                },
                                {
                                    jobName: '新签商家激活外呼',
                                    status: 'success',
                                    descriptions: [
                                        { label: '外呼商家', value: '500家' },
                                        { label: '已完成', value: '500家' },
                                    ],
                                    button: {
                                        text: '查看结果',
                                        action: 'submitQuestion',
                                        question: '查看外呼任务详细结果',
                                        type: 'primary',
                                    },
                                    createTime: 1705206000000,
                                    completeTime: 1705240200000,
                                    progress: 100,
                                },
                                {
                                    jobName: '新签商家激活外呼',
                                    status: 'success',
                                    descriptions: [
                                        { label: '外呼商家', value: '500家' },
                                        { label: '已完成', value: '500家' },
                                    ],
                                    button: {
                                        text: '查看结果',
                                        action: 'submitQuestion',
                                        question: '查看外呼任务详细结果',
                                        type: 'primary',
                                    },
                                    createTime: 1705206000000,
                                    completeTime: 1705240200000,
                                    progress: 100,
                                },
                                {
                                    jobName: '新签商家激活外呼',
                                    status: 'success',
                                    descriptions: [
                                        { label: '外呼商家', value: '500家' },
                                        { label: '已完成', value: '500家' },
                                    ],
                                    button: {
                                        text: '查看结果',
                                        action: 'submitQuestion',
                                        question: '查看外呼任务详细结果',
                                        type: 'primary',
                                    },
                                    createTime: 1705206000000,
                                    completeTime: 1705240200000,
                                    progress: 100,
                                },
                            ],
                            extendButtonName: '展开',
                            showNum: 2,
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // 综合场景：外呼任务完整流程 (重复)
    aiCallCompleteFlowDuplicate: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135630',
            msgId: '5135640',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: JSON.stringify([
                {
                    type: 'text',
                    insert: '基于您的商家数据分析，我们为您提供完整的外呼解决方案：',
                },
                {
                    type: 'actionCard',
                    insert: {
                        actionCard: {
                            title: '外呼任务管理中心',
                            subTitle: '一站式外呼任务创建、监控和分析平台',
                            backgroundColor: '#f6ffed',
                            buttonList: [
                                {
                                    text: '创建新任务',
                                    action: 'openAICallModal',
                                    AICallParams: {
                                        taskName: '商家激活外呼',
                                        callScript:
                                            '您好，恭喜您成功入驻美团外卖！我们想为您介绍一些运营技巧...',
                                        targetCount: 100,
                                    },
                                    type: 'primary',
                                },
                                {
                                    text: '查看历史',
                                    action: 'submitQuestion',
                                    question: '请显示我的外呼任务历史记录',
                                    type: 'normal',
                                },
                            ],
                        },
                    },
                },
                {
                    type: 'text',
                    insert: '\n最近任务执行情况：',
                },
                {
                    type: 'aiCallRecord',
                    insert: {
                        aiCallRecord: {
                            content: [
                                {
                                    jobName: '本周商家关怀外呼',
                                    status: 'running',
                                    descriptions: [
                                        { label: '外呼商家', value: '300家' },
                                        { label: '已完成', value: '234家' },
                                        { label: '成功接通', value: '180家' },
                                        { label: '失败', value: '54家' },
                                        {
                                            label: '创建时间',
                                            value: '2024.01.15 08:00:00',
                                        },
                                        {
                                            label: '最近更新',
                                            value: '2024.01.15 15:30:00',
                                        },
                                    ],
                                    button: {
                                        text: '查看进度',
                                        action: 'submitQuestion',
                                        question:
                                            '查看本周商家关怀外呼详细进度',
                                        type: 'normal',
                                    },
                                    createTime: 1705283200000, // 2024-01-15 08:00:00
                                    progress: 78,
                                },
                                {
                                    jobName: '商家激活提醒外呼',
                                    status: 'success',
                                    descriptions: [
                                        { label: '外呼商家', value: '150家' },
                                        { label: '已完成', value: '150家' },
                                        { label: '成功接通', value: '120家' },
                                        {
                                            label: '创建时间',
                                            value: '2024.01.14 16:00:00',
                                        },
                                        {
                                            label: '完成时间',
                                            value: '2024.01.15 10:00:00',
                                        },
                                    ],
                                    button: {
                                        text: '查看结果',
                                        action: 'submitQuestion',
                                        question: '查看商家激活提醒外呼结果',
                                        type: 'primary',
                                    },
                                    createTime: 1705231200000, // 2024-01-14 16:00:00
                                    completeTime: 1705291200000, // 2024-01-15 10:00:00
                                    progress: 100,
                                },
                                {
                                    jobName: '新签商家欢迎外呼',
                                    status: 'init',
                                    descriptions: [
                                        { label: '外呼商家', value: '200家' },
                                        {
                                            label: '计划开始',
                                            value: '2024.01.16 09:00:00',
                                        },
                                        {
                                            label: '创建时间',
                                            value: '2024.01.15 17:00:00',
                                        },
                                    ],
                                    button: {
                                        text: '立即开始',
                                        action: 'submitQuestion',
                                        question:
                                            '立即开始新签商家欢迎外呼任务',
                                        type: 'primary',
                                    },
                                    createTime: 1705316400000, // 2024-01-15 17:00:00
                                },
                            ],
                            redirectButton: {
                                text: '查看结果',
                                action: 'submitQuestion',
                                question: '查看商家激活提醒外呼结果',
                                type: 'primary',
                            },
                            extendButtonName: '查看更多',
                            showNum: 2,
                        },
                    },
                },
                {
                    type: 'text',
                    insert: '\n整体外呼效果分析：',
                },
                {
                    type: 'pieChart',
                    insert: {
                        pieChart: {
                            title: '本月外呼结果汇总',
                            data: [
                                {
                                    label: '成功接通',
                                    value: 1250,
                                    color: '#52c41a',
                                },
                                {
                                    label: '未接听',
                                    value: 680,
                                    color: '#faad14',
                                },
                                {
                                    label: '拒绝接听',
                                    value: 320,
                                    color: '#ff7a45',
                                },
                                {
                                    label: '号码异常',
                                    value: 150,
                                    color: '#d9d9d9',
                                },
                            ],
                            size: 220,
                            showLegend: true,
                        },
                    },
                },
            ]),
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // Form 表单数据 - 基础表单
    formBasic: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135631',
            msgId: '5135641',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '请填写以下信息以便为您提供更精准的服务：',
                        },
                    },
                },
                {
                    type: 'form',
                    insert: {
                        form: {
                            config: [
                                {
                                    label: 'BDmis',
                                    type: 'input',
                                    defaultValue: '',
                                },
                                {
                                    label: '绩效目标',
                                    type: 'radio',
                                    options: ['100%', '120%', '150%'],
                                    defaultValue: '100%',
                                },
                            ],
                            buttonText: '确定',
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // Form 表单数据 - 复杂表单
    formComplex: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135632',
            msgId: '5135642',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '商家信息登记表单，请完整填写以下信息：',
                        },
                    },
                },
                {
                    type: 'form',
                    insert: {
                        form: {
                            config: [
                                {
                                    label: '商家名称',
                                    type: 'input',
                                    defaultValue: '',
                                    required: true,
                                    message: '请输入商家名称',
                                },
                                {
                                    label: '联系电话',
                                    type: 'input',
                                    defaultValue: '',
                                    regExp: '^1[3-9]\\d{9}$',
                                    message: '请输入正确的手机号码',
                                    required: true,
                                },
                                {
                                    label: '商家类型',
                                    type: 'radio',
                                    options: [
                                        '餐饮',
                                        '生鲜',
                                        '商超',
                                        '医药',
                                        '其他',
                                    ],
                                    defaultValue: '餐饮',
                                },
                                {
                                    label: '经营规模',
                                    type: 'radio',
                                    options: [
                                        '小型(1-10人)',
                                        '中型(11-50人)',
                                        '大型(50人以上)',
                                    ],
                                    defaultValue: '小型(1-10人)',
                                },
                                {
                                    label: '详细地址',
                                    type: 'input',
                                    defaultValue: '',
                                    required: true,
                                    message: '请输入详细地址',
                                },
                            ],
                            buttonText: '提交信息',
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // Form 表单数据 - 外呼任务创建表单
    formAICall: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135633',
            msgId: '5135643',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '创建外呼任务，请填写以下配置信息：',
                        },
                    },
                },
                {
                    type: 'form',
                    insert: {
                        form: {
                            config: [
                                {
                                    label: '任务名称',
                                    type: 'input',
                                    defaultValue: '',
                                    required: true,
                                    message: '请输入任务名称',
                                },
                                {
                                    label: '外呼类型',
                                    type: 'radio',
                                    options: [
                                        '商家激活',
                                        '运营关怀',
                                        '问题回访',
                                        '满意度调研',
                                    ],
                                    defaultValue: '商家激活',
                                },
                                {
                                    label: '目标商家数',
                                    type: 'input',
                                    defaultValue: '100',
                                    regExp: '^[1-9]\\d*$',
                                    message: '请输入正确的数字',
                                    required: true,
                                },
                                {
                                    label: '执行时间',
                                    type: 'radio',
                                    options: [
                                        '立即执行',
                                        '定时执行',
                                        '手动执行',
                                    ],
                                    defaultValue: '立即执行',
                                },
                                {
                                    label: '话术模板',
                                    type: 'input',
                                    defaultValue: '您好，我是美团外卖的客服...',
                                    required: true,
                                    message: '请输入话术模板',
                                },
                            ],
                            buttonText: '创建任务',
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // Form 表单数据 - 数据查询表单
    formDataQuery: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135634',
            msgId: '5135644',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'markdown',
                    insert: {
                        markdown: {
                            text: '数据查询条件设置，请选择查询参数：',
                        },
                    },
                },
                {
                    type: 'form',
                    insert: {
                        form: {
                            config: [
                                {
                                    label: '查询维度',
                                    type: 'radio',
                                    options: [
                                        '按商家',
                                        '按区域',
                                        '按时间',
                                        '按品类',
                                    ],
                                    defaultValue: '按商家',
                                },
                                {
                                    label: '时间范围',
                                    type: 'radio',
                                    options: [
                                        '近7天',
                                        '近30天',
                                        '近90天',
                                        '自定义',
                                    ],
                                    defaultValue: '近7天',
                                },
                                {
                                    label: '商家ID',
                                    type: 'input',
                                    defaultValue: '',
                                    regExp: '^\\d+$',
                                    message: '请输入正确的商家ID',
                                    required: false,
                                },
                                {
                                    label: '数据类型',
                                    type: 'radio',
                                    options: [
                                        '订单数据',
                                        '营收数据',
                                        '用户数据',
                                        '运营数据',
                                    ],
                                    defaultValue: '订单数据',
                                },
                            ],
                            buttonText: '开始查询',
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },

    // Form 表单数据 - 带标题和副标题的表单
    formWithTitle: {
        code: 0,
        msg: '成功',
        data: {
            questionMsgId: '5135635',
            msgId: '5135645',
            type: 2,
            abilityType: 1,
            status: 1,
            sensitive: false,
            msgType: 1,
            currentContent: [
                {
                    type: 'form',
                    insert: {
                        form: {
                            title: '商家运营诊断',
                            subTitle:
                                '请填写相关信息，我们将为您提供专业的运营建议',
                            config: [
                                {
                                    label: '商家ID',
                                    type: 'input',
                                    defaultValue: '',
                                    regExp: '^\\d+$',
                                    message: '请输入正确的商家ID',
                                    required: true,
                                },
                                {
                                    label: '诊断类型',
                                    type: 'radio',
                                    options: [
                                        '全面诊断',
                                        '订单分析',
                                        '用户分析',
                                        '竞品分析',
                                    ],
                                    defaultValue: '全面诊断',
                                },
                                {
                                    label: '关注重点',
                                    type: 'radio',
                                    options: [
                                        '提升订单量',
                                        '提高客单价',
                                        '增加复购率',
                                        '优化评分',
                                    ],
                                    defaultValue: '提升订单量',
                                },
                            ],
                            buttonText: '开始诊断',
                        },
                    },
                },
            ],
            previousContent: null,
            prefixTextContent: null,
            postTextContent: null,
            selectionItems: null,
            imageList: null,
            feedbackType: null,
            subAbilityType: null,
            hasNext: false,
            pageNum: null,
            respTime: 1752117187133,
            tags: null,
        },
    },
};
