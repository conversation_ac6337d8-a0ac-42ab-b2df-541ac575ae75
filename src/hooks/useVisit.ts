import { Toast } from '@roo/roo-rn';
import { useState, useEffect, useCallback } from 'react';

import {
    getRecommendCheckInPois,
    getRecommendCheckOutPoi,
    getUserReminderConfig,
    updateUserReminderConfig,
    signInPoi,
    signOutPoi,
    PoiInfo,
    SignOutPoiInfo,
    ReminderConfig,
} from '../api/visitApi';
import {
    refreshLocation,
    startLocationTracking,
    stopLocationTracking,
} from '../utils/locationService';
import { trackEvent } from '../utils/track';

export interface UseVisitReturn {
    // 签入相关
    signInPoiList: PoiInfo[];
    isLoadingSignInPois: boolean;
    refreshSignInPois: () => Promise<void>;
    handlePoiSignIn: (poiId: number) => void;

    // 签出相关
    signOutPoiInfo: SignOutPoiInfo | null;
    isLoadingSignOutPoi: boolean;
    refreshSignOutPoi: () => Promise<void>;
    handlePoiSignOut: (url: string) => void;

    // 提醒配置相关
    reminderConfig: ReminderConfig;
    isLoadingReminderConfig: boolean;
    updateReminderConfig: (config: ReminderConfig) => Promise<boolean>;

    // 位置相关
    refreshUserLocation: () => Promise<void>;
    startTracking: () => void;
    stopTracking: () => void;
}

/**
 * 拜访相关的Hook
 * @returns UseVisitReturn
 */
export const useVisit = (): UseVisitReturn => {
    // 签入商家列表
    const [signInPoiList, setSignInPoiList] = useState<PoiInfo[]>([]);
    const [isLoadingSignInPois, setIsLoadingSignInPois] = useState(false);

    // 签出商家信息
    const [signOutPoiInfo, setSignOutPoiInfo] = useState<SignOutPoiInfo | null>(
        null,
    );
    const [isLoadingSignOutPoi, setIsLoadingSignOutPoi] = useState(false);

    // 提醒配置
    const [reminderConfig, setReminderConfig] = useState<ReminderConfig>({
        switch: true,
        period: 'daily',
        startTime: '08:00',
        endTime: '18:00',
    });
    const [isLoadingReminderConfig, setIsLoadingReminderConfig] =
        useState(false);

    // 获取签入商家列表
    const fetchSignInPois = useCallback(
        async (_forceRecommend: boolean = false) => {
            setIsLoadingSignInPois(true);
            try {
                // 使用mock数据
                const pois = await getRecommendCheckInPois();
                setSignInPoiList(pois);
            } catch (error) {
                console.error('获取签入商家列表失败:', error);
                Toast.open('获取签入商家列表失败');
            } finally {
                setIsLoadingSignInPois(false);
            }
        },
        [],
    );

    // 获取签出商家信息
    const fetchSignOutPoi = useCallback(async () => {
        setIsLoadingSignOutPoi(true);
        try {
            // 使用mock数据
            const poiInfo = await getRecommendCheckOutPoi();
            setSignOutPoiInfo(poiInfo);
        } catch (error) {
            console.error('获取签出商家信息失败:', error);
        } finally {
            setIsLoadingSignOutPoi(false);
        }
    }, []);

    // 获取提醒配置
    const fetchReminderConfig = useCallback(async () => {
        setIsLoadingReminderConfig(true);
        try {
            const config = await getUserReminderConfig();
            setReminderConfig(config);
        } catch (error) {
            console.error('获取提醒配置失败:', error);
        } finally {
            setIsLoadingReminderConfig(false);
        }
    }, []);

    // 刷新签入商家列表
    const refreshSignInPois = useCallback(async () => {
        try {
            await refreshLocation();
            await fetchSignInPois(true);
            trackEvent('visit_refresh_location');
        } catch (error) {
            console.error('刷新签入商家列表失败:', error);
            Toast.open('刷新位置失败');
        }
    }, [fetchSignInPois]);

    // 刷新签出商家信息
    const refreshSignOutPoi = useCallback(async () => {
        try {
            await refreshLocation();
            await fetchSignOutPoi();
        } catch (error) {
            console.error('刷新签出商家信息失败:', error);
        }
    }, [fetchSignOutPoi]);

    // 刷新用户位置
    const refreshUserLocation = useCallback(async () => {
        try {
            await refreshLocation();
            Toast.open('位置刷新成功');
            // 刷新商家列表
            fetchSignInPois(true);
        } catch (error) {
            console.error('刷新位置失败:', error);
            Toast.open('刷新位置失败');
        }
    }, [fetchSignInPois]);

    // 处理商家签入
    const handlePoiSignIn = useCallback(
        async (poiId: number) => {
            try {
                // 使用mock数据模拟签入
                const result = await signInPoi(poiId);
                if (result.success) {
                    Toast.open(result.message);
                    // 刷新商家列表
                    fetchSignInPois(true);
                } else {
                    Toast.open('签入失败，请重试');
                }
                trackEvent('visit_poi_sign_in', { poi_id: poiId });
            } catch (error) {
                console.error('签入失败:', error);
                Toast.open('签入失败，请重试');
            }
        },
        [fetchSignInPois],
    );

    // 处理商家签出
    const handlePoiSignOut = useCallback(
        async (url: string) => {
            try {
                // 解析URL获取poiId
                const poiId = parseInt(url.split('poiId=')[1], 10) || 0;

                // 使用mock数据模拟签出
                const result = await signOutPoi(poiId);
                if (result.success) {
                    Toast.open(result.message);
                    // 刷新商家信息
                    fetchSignOutPoi();
                } else {
                    Toast.open('签出失败，请重试');
                    // 如果需要跳转到拜访签出页面
                    // Linking.openURL(url);
                }
                trackEvent('visit_poi_sign_out');
            } catch (error) {
                console.error('签出失败:', error);
                Toast.open('签出失败，请重试');
                // 如果需要跳转到拜访签出页面
                // Linking.openURL(url);
            }
        },
        [fetchSignOutPoi],
    );

    // 更新提醒配置
    const updateConfig = useCallback(async (config: ReminderConfig) => {
        try {
            const success = await updateUserReminderConfig(config);
            if (success) {
                setReminderConfig(config);
                Toast.open('提醒配置更新成功');
                trackEvent('visit_update_reminder_config');
            } else {
                Toast.open('提醒配置更新失败');
            }
            return success;
        } catch (error) {
            console.error('更新提醒配置失败:', error);
            Toast.open('提醒配置更新失败');
            return false;
        }
    }, []);

    // 初始化
    useEffect(() => {
        // 获取初始数据
        fetchSignInPois();
        fetchSignOutPoi();
        fetchReminderConfig();

        // 开始位置跟踪
        startLocationTracking();

        // 组件卸载时停止位置跟踪
        return () => {
            stopLocationTracking();
        };
    }, [fetchSignInPois, fetchSignOutPoi, fetchReminderConfig]);

    return {
        signInPoiList,
        isLoadingSignInPois,
        refreshSignInPois,
        handlePoiSignIn,

        signOutPoiInfo,
        isLoadingSignOutPoi,
        refreshSignOutPoi,
        handlePoiSignOut,

        reminderConfig,
        isLoadingReminderConfig,
        updateReminderConfig: updateConfig,

        refreshUserLocation,
        startTracking: startLocationTracking,
        stopTracking: stopLocationTracking,
    };
};
