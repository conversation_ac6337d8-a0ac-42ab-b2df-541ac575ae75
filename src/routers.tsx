/* eslint-disable */
import {
    getDefaultNavigatorOptions,
    withSafeAreaView,
    withStackNavigator,
    withAdaptProps,
    parseMRNData,
    NavigationContainer,
} from '@mfe/bee-foundation-navigation';
import {
    createNativeStackNavigator,
    NativeStackScreenProps,
} from '@react-navigation/native-stack';
import * as React from 'react';

import Chat from './pages/chat/ChatEntry';
import Instruction from './pages/chat/Instruction';
import Complain from './pages/complain/Complain';
import ConfigFormDemo from './pages/configFormDemo';
import HorizontalTable from './pages/horizontalTable/HorizontalTable';
import DraggedFlatListDemo from './pages/draggedFlatListDemo/demo';

export type RootStackParamList = {
    Chat: undefined | {
        triggerQuery?: string;
        aichat_scene_id?: string;
        pageSize?: number;
    };
    Instruction: undefined;
    Complain: undefined;
    ConfigFormDemo: undefined;
    HorizontalTable: undefined;
    DraggedFlatListDemo: undefined;
    DraggedMenuDemo: undefined;
};

export type NavigationProps = NativeStackScreenProps<RootStackParamList>;

const Stack = createNativeStackNavigator<RootStackParamList>();

const routes: {
    name: string;
    component: React.ComponentType<any>;
    params?: Record<string, any>;
}[] = [
    {
        name: 'Chat',
        component: Chat,
    },
    {
        name: 'Complain',
        component: Complain,
    },
    {
        name: 'ConfigFormDemo',
        component: ConfigFormDemo,
    },
    {
        name: 'HorizontalTable',
        component: HorizontalTable,
    },
    {
        name: 'DraggedFlatListDemo',
        component: DraggedFlatListDemo,
    },
    {
        name: 'Instruction',
        component: Instruction,
    },
];

export function StackRouter(props) {
    const initialRoute = props.initialRoute || 'Chat';

    const data = parseMRNData(props);
    return (
        <NavigationContainer>
            <Stack.Navigator
                /// 设置初始化加载的路由名称
                initialRouteName={initialRoute}
                /// getDefaultNavigatorOptions是蜜蜂默认navigation样式。默认标题栏是隐藏状态。
                screenOptions={getDefaultNavigatorOptions}
            >
                {routes.map((route) => (
                    <Stack.Screen
                        key={route.name}
                        name={route.name as keyof RootStackParamList}
                        component={withStackNavigator(
                            withSafeAreaView(withAdaptProps(route.component)),
                        )}
                        initialParams={
                            initialRoute === route.name ? data : {}
                        }
                        {...(route.params || {})}
                    />
                ))}
            </Stack.Navigator>
        </NavigationContainer>
    );
}
