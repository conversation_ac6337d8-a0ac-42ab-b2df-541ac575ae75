import React, { createContext, useContext, ReactNode } from 'react';

import { useVisit } from '../hooks/useVisit';
import { useMessageListener } from '../utils/messageListener';

// 创建上下文
const VisitContext = createContext<ReturnType<typeof useVisit> | null>(null);

interface VisitProviderProps {
    children: ReactNode;
}

/**
 * 拜访上下文提供器
 * @param props
 * @returns
 */
export const VisitProvider: React.FC<VisitProviderProps> = ({ children }) => {
    // 使用拜访Hook
    const visit = useVisit();

    // 使用消息监听Hook
    useMessageListener(visit);

    return (
        <VisitContext.Provider value={visit}>{children}</VisitContext.Provider>
    );
};

/**
 * 使用拜访上下文的Hook
 * @returns 拜访上下文
 */
export const useVisitContext = (): ReturnType<typeof useVisit> => {
    const context = useContext(VisitContext);

    if (!context) {
        throw new Error('useVisitContext must be used within a VisitProvider');
    }

    return context;
};
