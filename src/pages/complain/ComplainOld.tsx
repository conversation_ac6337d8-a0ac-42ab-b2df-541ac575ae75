import LX from '@analytics/mrn-sdk';
import <PERSON><PERSON><PERSON><PERSON>, { Source } from '@mfe/bee-foundation-moses';
import { useSafeAreaInsets } from '@mfe/bee-foundation-navigation';
import { View, Text, StatusBar } from '@mrn/react-native';
import { NavigationBar, Button } from '@roo/roo-rn';
import React from 'react';

import styles from './styles';

type Callback = () => void;
interface Props {
    navigation: { goBack: () => void };
    handleJumpReporter: Callback;
    handleJumpResolved: Callback;
    handleJumpTTList: Callback;
    myResolvedTTNumber: number;
    myReporterTTNumber: number;
    data: { source: Source };
    source?: Source;
}

const BackgroundColor = '#212531';
const pageInfoKey = LX.getPageInfoKey('c_waimai_e_bee_mine');
const ComplainOld = (props: Props) => {
    const {
        handleJumpReporter,
        handleJumpResolved,
        handleJumpTTList,
        myResolvedTTNumber,
        myReporterTTNumber,
    } = props;

    const insets = useSafeAreaInsets();
    const statusBarHeight = StatusBar.currentHeight || insets.top;

    return (
        <View>
            <StatusBar
                backgroundColor="rgba(255,255,255,0)"
                barStyle="light-content"
                translucent // 仅对安卓生效，意味可将内容渲染到status，iOS默认支持
            />
            <View
                style={{
                    paddingTop: statusBarHeight,
                    marginTop: -statusBarHeight,
                    backgroundColor: BackgroundColor,
                }}
            />
            <NavigationBar
                titleStyles={{ color: '#fff' }}
                backgroundColor={BackgroundColor}
                backButtonTintColor={'#fff'}
                title={'在线提问'}
                onPressBackButton={() => {
                    props.navigation.goBack();
                }}
            />
            <View>
                <View style={styles.ttEntry}>
                    <View style={styles.ttInfo}>
                        <View
                            style={{
                                backgroundColor: BackgroundColor,
                                paddingTop: 30,
                                width: '100%',
                                alignItems: 'center',
                            }}
                        >
                            <Text
                                style={styles.ttTitle}
                                onPress={handleJumpReporter}
                            >
                                待我处理
                            </Text>
                        </View>

                        <Text
                            style={styles.ttNumber}
                            onPress={handleJumpReporter}
                        >
                            {myResolvedTTNumber}
                        </Text>
                    </View>
                    <View style={styles.ttInfo}>
                        <View
                            style={{
                                backgroundColor: BackgroundColor,
                                paddingTop: 30,
                                width: '100%',
                                alignItems: 'center',
                            }}
                        >
                            <Text
                                style={styles.ttTitle}
                                onPress={handleJumpResolved}
                            >
                                我发起的
                            </Text>
                        </View>

                        <Text
                            style={styles.ttNumber}
                            onPress={handleJumpResolved}
                        >
                            {myReporterTTNumber}
                        </Text>
                    </View>
                </View>
                <View style={styles.contentContainer}>
                    <View style={styles.buttonContainer}>
                        <View style={{}}>
                            <Button
                                type="default"
                                style={styles.buttonBorder}
                                onPress={handleJumpTTList}
                            >
                                <Text>工单列表</Text>
                            </Button>
                        </View>
                        <View style={{}}>
                            <MosesContainer
                                source={props.source}
                                // @ts-ignore
                                beforeJump={() => {
                                    LX.moduleClick({
                                        cid: 'c_waimai_e_bee_mine',
                                        bid: 'b_waimai_e_bee_mine_assistant_question_mc',
                                        pageInfoKey,
                                    });
                                    return Promise.resolve(true);
                                }}
                            >
                                {/* 我要提问 */}
                                <Button
                                    type="primary"
                                    style={styles.buttonBackground}
                                    // onPress={this.handleGoToMoses}
                                >
                                    <Text style={{ color: '#fff' }}>
                                        我要提问
                                    </Text>
                                </Button>
                            </MosesContainer>
                        </View>
                    </View>
                </View>
            </View>
        </View>
    );
};
export default ComplainOld;
