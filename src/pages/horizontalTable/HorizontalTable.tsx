import { useNavigation } from '@mfe/bee-foundation-navigation';
import KNB from '@mrn/mrn-knb';
import { ScrollView, TouchableOpacity, View } from '@mrn/react-native';
import { Toast, Icon } from '@roo/roo-rn';
import React, { useEffect } from 'react';
import Orientation from 'react-native-orientation';

import MarkdownTable from '@/components/MarkdownTable';
import RNText from '@/components/RNText';
import TWS from '@/TWS';

interface Props {
    mdData: string;
    title?: string;
}

const HorizontalTable = ({ mdData, title = '' }: Props) => {
    useEffect(() => {
        // 设置屏幕为横向
        Orientation.lockToLandscape();

        // 组件卸载时恢复默认方向
        return () => {
            Orientation.lockToPortrait();
        };
    }, []);

    const { goBack } = useNavigation();
    return (
        <View
            style={{
                flex: 1,
                paddingTop: 16,
            }}
        >
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    paddingHorizontal: 16,
                }}
            >
                <RNText
                    style={{ fontSize: 18, fontWeight: '500', color: '#222' }}
                >
                    {title}
                </RNText>
                <TouchableOpacity onPress={goBack} style={[TWS.row()]}>
                    <Icon type="rotate" size={16} tintColor="#666" />
                    <RNText
                        style={{ fontSize: 14, color: '#666', marginLeft: 4 }}
                    >
                        返回竖屏
                    </RNText>
                </TouchableOpacity>
            </View>

            <ScrollView style={{ paddingHorizontal: 16 }}>
                <MarkdownTable
                    markdownContent={mdData}
                    onLinkPress={(url) => {
                        if (!url) {
                            return Toast.open('url为空');
                        }
                        KNB.openPage({ url });
                    }}
                />
            </ScrollView>
        </View>
    );
};

export default HorizontalTable;
