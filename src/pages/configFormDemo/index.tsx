import { View, ScrollView, Text, StyleSheet } from '@mrn/react-native';
import React from 'react';

import {
    mockConfigFormData,
    mockVisitReminderConfigForm,
} from '../../api/mockData';
import { ConfigForm } from '../../components/Chat/MessageComponent/ConfigForm';

/**
 * ConfigForm组件演示页面
 * 用于测试和展示ConfigForm组件的各种用法
 */
const ConfigFormDemo: React.FC = () => {
    // 基础示例数据
    const basicData = mockConfigFormData();

    // 拜访提醒设置数据
    const visitReminderData = mockVisitReminderConfigForm();

    // 自定义配置数据
    const customData = {
        config: [
            {
                label: '开启通知',
                labelStyle: 'bold' as const,
                type: 'switch' as const,
                defaultValue: 'true',
            },
            {
                label: '通知类型',
                type: 'select' as const,
                options: ['推送通知', '短信通知', '邮件通知'],
                defaultValue: '推送通知',
            },
            {
                label: '工作时间',
                type: 'timeRangePicker' as const,
                defaultValue: '09:00-18:00',
            },
        ],
        formId: 'notificationSettings',
        buttonText: '确定',
    };

    return (
        <ScrollView style={styles.container}>
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>基础示例</Text>
                <View style={styles.formContainer}>
                    <ConfigForm {...basicData.insert.configForm} />
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>
                    拜访提醒设置（匹配UI设计）
                </Text>
                <View style={styles.formContainer}>
                    <ConfigForm {...visitReminderData.insert.configForm} />
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>自定义配置示例</Text>
                <View style={styles.formContainer}>
                    <ConfigForm {...customData} />
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>历史消息状态（已提交）</Text>
                <View style={styles.formContainer}>
                    <ConfigForm
                        {...basicData.insert.configForm}
                        history={true}
                    />
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>使用说明</Text>
                <View style={styles.instructionContainer}>
                    <Text style={styles.instructionText}>
                        1.
                        支持三种表单控件：switch（开关）、select（下拉选择）、timeRangePicker（时间范围选择器）
                    </Text>
                    <Text style={styles.instructionText}>
                        2.
                        表单提交时会发送JSON格式的hideSpan消息，包含formType和所有字段值
                    </Text>
                    <Text style={styles.instructionText}>
                        3. 历史消息状态下，表单控件会被禁用且按钮隐藏
                    </Text>
                    <Text style={styles.instructionText}>
                        4. 支持labelStyle='bold'来设置标签为粗体样式
                    </Text>
                </View>
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#F5F6FA',
    },
    section: {
        marginBottom: 24,
        paddingHorizontal: 16,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#222222',
        marginBottom: 12,
        marginTop: 16,
    },
    formContainer: {
        backgroundColor: '#FFFFFF',
        borderRadius: 12,
        padding: 4,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    instructionContainer: {
        backgroundColor: '#FFFFFF',
        borderRadius: 12,
        padding: 16,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    instructionText: {
        fontSize: 14,
        color: '#666666',
        lineHeight: 20,
        marginBottom: 8,
    },
});

export default ConfigFormDemo;
