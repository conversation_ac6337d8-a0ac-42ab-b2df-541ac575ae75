import { useSafeAreaInsets } from '@mfe/bee-foundation-navigation';
import { apiCaller } from '@mfe/cc-api-caller-bee';
import KNB from '@mrn/mrn-knb';
import {
    Animated,
    Dimensions,
    KeyboardAvoidingView,
    LayoutAnimation,
    NativeScrollEvent,
    NativeSyntheticEvent,
    Platform,
    ScrollView,
    StatusBar,
    TextInput,
    UIManager,
    View,
    // @ts-ignore
    unstable_RootTagContext,
    BackHandler,
} from '@mrn/react-native';
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';

import useBg from './hooks/useBg';
import AbilitySelector from '../../components/AbilitySelector/AbilitySelectorNew';
import ChatContent from '../../components/Chat/ChatContent';
import ChatFooter from '../../components/Chat/ChatFooter/ChatFooterNew';
import ChatHeader from '../../components/Chat/ChatHeader/ChatHeaderNew';
import PoiSelector from '../../components/Chat/PoiSelector/PoiSelector';
import VoiceInput2 from '../../components/VoiceInput/VoiceInput2';
import { useStartChat } from '../../hooks/biz/useStartChat';
import RootTagContext from '../../hooks/rootTagContext';
import useKeyboard from '../../hooks/useKeyboard';
import useMessage, { getUseMessage } from '../../hooks/useMessage';
import useVoiceInput from '../../hooks/useVoiceInput';
import { ModalWrapper } from '../../store/slideModal';
import { useTaskStore } from '../../store/task';
import { useUiState } from '../../store/uiState';
import { SOURCE, EntryPointType } from '../../types';
import getConditionStyle from '../../utils/getConditionStyle';
import { track } from '../../utils/track';

import { TabType } from '@/components/Chat/ChatHeader/ChatHeaderNew';
import ChatHome from '@/components/Chat/ChatHome/ChatHome';
import Condition from '@/components/Condition/Condition';
import GuideManager from '@/components/GuideModal/GuideManager';
import InputAssociation from '@/components/InputAssociation';
import RNImage from '@/components/RNImage';
import { TaskListContent } from '@/components/TaskList/TaskListDrawer';
import { useBizInfo } from '@/hooks/useBizInfo';
import useCallerRequest from '@/hooks/useCallerRequest';
import { useSendMessage } from '@/hooks/useSendMessage';

const screenWidth = Dimensions.get('window').width;

interface Chat {}

const Wrapper = ({ offset, style = {}, focused, ...props }) => {
    const { keyboardOffset, isKeyboardShow } = useKeyboard();

    const innerStyle = useMemo(() => {
        if (Platform.OS === 'ios') {
            return { flex: 1, paddingBottom: focused ? keyboardOffset : 0 };
        }
        if (!isKeyboardShow) {
            return {
                flex: 1,
            };
        }
        try {
            UIManager.setLayoutAnimationEnabledExperimental(true);
            LayoutAnimation.configureNext(
                LayoutAnimation.Presets.easeInEaseOut,
            );
        } catch (e) {
            console.log(e);
        }
        return {
            height:
                Dimensions.get('window').height -
                keyboardOffset -
                StatusBar.currentHeight,
        };
    }, [keyboardOffset, offset.current, isKeyboardShow]);

    return Platform.OS === 'ios' ? (
        <KeyboardAvoidingView
            {...props}
            behavior={'padding'}
            style={[innerStyle, style]}
        />
    ) : (
        <View {...props} style={[innerStyle, style]} />
    );
};

// Chat页面可能直接挂载在主包路由中，因此不能直接使用useNavigation
// 如果需要跳转，请使用props.navigator.push/pop
// 这个方法在通过业务子包方式打开时，会使用useNavigation().navigation
// 在通过直接挂载主包的方式打开时，会使用主包路由的navigator
const Chat = (props) => {
    const insets = useSafeAreaInsets();
    const statusBarHeight = StatusBar.currentHeight || insets.top;
    const disconnect = useMessage((state) => state.disconnect);
    const reset = useMessage((state) => state.reset);
    const showHome = useUiState((state) => state.showHome);
    const { openDrawer } = useTaskStore();
    const callerRequest = useCallerRequest();

    // Tab状态管理 - 从 useMessage 中获取
    const activeTab = useMessage((state) => state.activeTab);
    const setActiveTab = useMessage((state) => state.setActiveTab);

    // 引导状态管理
    const [showGuide, setShowGuide] = useState(false);
    const [currentGuideStep, setCurrentGuideStep] = useState(0);
    const [guideLoading, setGuideLoading] = useState(true);

    // Tab切换处理
    const handleTabChange = (tab: TabType) => {
        setActiveTab(tab);
    };

    // 引导步骤定义
    const guideSteps = [
        {
            key: 'skillCard1',
            title: '这里是商家智能沟通工具：',
            description: '轻松发起商家批量沟通',
            placement: 'top' as const,
        },
        {
            key: 'skillCard2',
            title: '这里是智能诊断：',
            description: '即时诊断您的绩效目标达成、商家经营状况',
            placement: 'top' as const,
        },
        {
            key: 'shortcuts',
            title: '这里是AI技能工具箱：',
            description: '查看点评分，计算到手价... ',
            placement: 'top' as const,
        },
        {
            key: 'taskTab',
            title: '这里是任务历史记录：',
            description: '查看您的外呼、绩效诊断等任务记录',
            placement: 'bottom' as const,
        },
    ];

    // 计算引导可见性：前3步需要在主页，第4步（任务tab）可以在任何页面
    const isGuideVisible =
        !guideLoading && showGuide && (showHome || currentGuideStep === 3);

    // 引导处理
    const handleGuideClose = async () => {
        try {
            await callerRequest.post(
                '/bee/v2/bdaiassistant/customConfig/saveOrUpdate',
                { type: 'skill_guide', config: 'v1' },
            );
            setShowGuide(false);
        } catch (error) {
            console.error('跳过引导失败:', error);
            // 即使接口失败也关闭引导
            setShowGuide(false);
        }
    };

    const handleGuideComplete = async () => {
        try {
            await callerRequest.post(
                '/bee/v2/bdaiassistant/customConfig/saveOrUpdate',
                { type: 'skill_guide', config: 'v1' },
            );
            setShowGuide(false);
        } catch (error) {
            console.error('完成引导失败:', error);
            // 即使接口失败也关闭引导
            setShowGuide(false);
        }
    };

    const sessionId = useMessage((state) => state.sessionId);

    const fetched = useRef(false);
    const { bizId } = useBizInfo();
    // 获取引导配置 - 等待sessionId就绪后再发起请求
    useEffect(() => {
        // 如果sessionId还没有就绪，不发起请求
        if (!sessionId.length || fetched.current || !bizId) {
            return;
        }

        fetched.current = true;
        const fetchGuideConfig = async () => {
            try {
                const response = await callerRequest.post(
                    '/bee/v2/bdaiassistant/customConfig/get',
                    { types: ['skill_guide'] },
                );

                if (response.code === 0 && response.data) {
                    // 检查是否需要显示引导
                    let guideConfig = response.data.customConfigList.find(
                        (config) => config.type === 'skill_guide',
                    );

                    // 如果配置不存在或content为'false'，则显示引导
                    const shouldShowGuide =
                        bizId == '5001' && guideConfig?.config !== 'v1';

                    setShowGuide(shouldShowGuide);
                }
            } catch (error) {
                console.error('BEE_ASSISTANT_DEBUG_获取引导配置失败:', error);
                // 接口失败时默认显示引导
                setShowGuide(true);
            } finally {
                setGuideLoading(false);
            }
        };

        fetchGuideConfig();
    }, [callerRequest, sessionId.length, bizId]);

    useEffect(() => {
        if (props.openTaskDrawer) {
            openDrawer();
        }
    }, [props.openTaskDrawer]);

    useStartChat(props);

    const scrollRef = useRef<ScrollView>();
    const inputRef = useRef<TextInput>();

    const scrollY = new Animated.Value(0);

    const moveParams = useVoiceInput();
    const { panResponder, openVoiceInput, voiceInputOpen, viewKey } =
        moveParams;

    const onScroll = ({
        nativeEvent,
    }: NativeSyntheticEvent<NativeScrollEvent>) => {
        return scrollY.setValue(nativeEvent.contentOffset.y);
    };

    const getLatestSessionId = useMessage((v) => v.getLatestSessionId);
    // 页面退出的时候，关闭轮询
    useEffect(() => {
        track('chat');
        return () => {
            apiCaller.send(
                '/bee/v1/bdaiassistant/closeSession',
                {
                    sessionId: getLatestSessionId(),
                },
                { silent: true },
            );
            // 通知首页弹出评价弹窗
            KNB.publish({
                action: 'ASSISTANT_CLOSE',
                data: { from: 'chat', to: props.source || SOURCE.home },
                success: () => {
                    console.log('ASSISTANT_CLOSE success');
                },
                fail: () => {
                    console.log('ASSISTANT_CLOSE fail');
                },
            });
            disconnect();
            reset();
        };
    }, []);

    const offset = useRef(Platform.OS === 'ios' ? insets.bottom : 20);
    useEffect(() => {
        // iOS在键盘消失后，insets.bottom可能会变化，所以这里只取第一次的默认值
        if (offset.current) {
            return;
        }

        offset.current = Platform.OS === 'ios' ? insets.bottom || 34 : 20;
    }, [insets.bottom]);

    const paddingOffset = Platform.OS === 'ios' ? 0 : 15;

    const focused = useMessage((s) => s.input.focused);
    const setFocused = useMessage((s) => s.input.setFocused);

    const { setPanelOpen } = useUiState();

    const bg = useBg();

    const { send } = useSendMessage();
    if (!sessionId.length) {
        return null;
    }

    return (
        <>
            <GuideManager
                steps={guideSteps}
                visible={isGuideVisible}
                onClose={handleGuideClose}
                onComplete={handleGuideComplete}
                onStepChange={setCurrentGuideStep}
            >
                {({ wrapWithTooltip, currentStep }) => (
                    <Wrapper
                        onTouchStart={() => {
                            setPanelOpen(false);
                        }}
                        {...panResponder.panHandlers}
                        offset={offset}
                        focused={focused}
                    >
                        <View
                            onLayout={(e) => {
                                console.log('onLayout', e.nativeEvent.layout);
                            }}
                            style={[
                                {
                                    flex: 1,
                                    backgroundColor: '#f5f6fa',
                                    marginTop: -statusBarHeight - 12, // 上移，对statusbar添加背景色
                                    paddingTop: statusBarHeight + 12, // 修正上移导致的内容偏上
                                },
                                getConditionStyle(
                                    {
                                        marginBottom: -insets.bottom, // 覆盖touchbar底色
                                        paddingBottom:
                                            insets.bottom || paddingOffset, // 修正为了为了覆盖底色导致的内容底部扩大，ios为底部touchbar的高度，安卓则用默认值
                                    },
                                    Platform.OS === 'ios',
                                ),
                            ]}
                        >
                            {/*Assistant Logo测试代码，后续Assistant UI迭代可以打开注释进行调试*/}
                            {/* <Assistant source="hhh" /> */}
                            <RNImage
                                source={{ uri: bg }}
                                style={[
                                    {
                                        width: screenWidth,
                                        position: 'absolute',
                                    },
                                ]}
                            />
                            <StatusBar
                                backgroundColor="rgba(255,255,255,0)"
                                barStyle="light-content"
                                translucent // 仅对安卓生效，意味可将内容渲染到status，iOS默认支持
                            />

                            <ChatHeader
                                {...props}
                                scrollHeight={scrollY}
                                onTabChange={handleTabChange}
                                wrapWithTooltip={wrapWithTooltip}
                                currentStep={currentStep}
                            />

                            <Condition
                                condition={[
                                    !showHome && activeTab === TabType.DIALOGUE,
                                    showHome && activeTab === TabType.DIALOGUE,
                                    activeTab === TabType.TASK,
                                ]}
                            >
                                <ChatContent
                                    ref={scrollRef}
                                    inputRef={inputRef}
                                    onScroll={onScroll}
                                />
                                <ChatHome
                                    showGuide={false} // 现在在顶层管理引导
                                    guideSteps={guideSteps}
                                    onGuideComplete={handleGuideComplete}
                                    wrapWithTooltip={wrapWithTooltip}
                                />
                                <TaskListContent
                                    onSendMessage={(content) => {
                                        // 任务列表中点击查看结果时，切换到对话tab并发送消息
                                        setActiveTab(TabType.DIALOGUE);
                                        // 需要同步到Header的tab状态，这里需要额外处理
                                        send({ content }, EntryPointType.USER);
                                    }}
                                />
                            </Condition>

                            <Condition
                                condition={[activeTab === TabType.DIALOGUE]}
                            >
                                <ChatFooter
                                    voiceInputOpen={voiceInputOpen}
                                    onVoiceLongPress={() => openVoiceInput()}
                                    ref={inputRef}
                                    offset={offset.current}
                                    onFocus={() => setFocused(true)}
                                    onBlur={() => setFocused(false)}
                                />
                            </Condition>
                        </View>
                        <Condition condition={[activeTab === TabType.DIALOGUE]}>
                            <AbilitySelector />
                        </Condition>
                        <InputAssociation />
                        <PoiSelector />
                        <ModalWrapper />
                    </Wrapper>
                )}
            </GuideManager>

            <VoiceInput2
                key={viewKey}
                style={[
                    {
                        marginTop: -statusBarHeight - 12, // 上移，对statusbar添加背景色
                        paddingTop: statusBarHeight + 12, // 修正上移导致的内容偏上
                    },
                    Platform.OS === 'ios'
                        ? {
                              marginBottom: -insets.bottom, // 覆盖touchbar底色
                              paddingBottom: insets.bottom || paddingOffset,
                          }
                        : {},
                ]}
                {...moveParams}
            />
        </>
    );
};

export default (props) => {
    const rootTag = useContext(unstable_RootTagContext);
    const uiState = useUiState();
    const useMessage = useRef(getUseMessage(props.rootTag || rootTag, uiState));

    useEffect(() => {
        uiState.reset();
        if (!props.rootTag) {
            return;
        }
        useMessage.current = getUseMessage(props.rootTag, uiState);
    }, [props.rootTag]);

    const [backing, setBacking] = useState(false);
    const pop = () => {
        setBacking(true);
        setTimeout(props.navigator.pop, 200);
    };

    useEffect(() => {
        if (Platform.OS !== 'android') {
            return;
        }
        const remove = BackHandler.addEventListener('hardwareBackPress', () => {
            pop();
            return true;
        });
        return remove.remove;
    }, []);
    const { bizId } = useBizInfo();

    if (!useMessage.current) {
        return null;
    }

    return (
        <RootTagContext.Provider
            value={{
                rootTag: props?.rootTag,
                useMessage: useMessage.current,
                pop,
                backing,
                source: props.source,
                extra: props.extra,
                bizId,
            }}
        >
            <Chat {...props} />
        </RootTagContext.Provider>
    );
};
