import { Keyboard, TouchableWithoutFeedback, View } from '@mrn/react-native';
import React, { ReactElement, useRef } from 'react';
import { create } from 'zustand';

import Condition from '../components/Condition/Condition';

interface SlideModalState {
    modalOpen: boolean;
    modalContent: ReactElement;
}

const defaultUiState: SlideModalState = {
    modalOpen: false,
    modalContent: null,
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const getActions = (set: Setter, _get: Getter) => ({
    open: (content: ReactElement) => {
        Keyboard.dismiss();
        set({ modalContent: content, modalOpen: true });
    },
    close: () => {
        set({ modalOpen: false });
    },
});
type UiStateAndActions = SlideModalState & ReturnType<typeof getActions>;
type Setter = (v: Partial<SlideModalState>) => void;
type Getter = () => SlideModalState;

export const useSlideModalState = create<UiStateAndActions>((set, get) => ({
    ...defaultUiState,
    ...getActions(set, get),
}));

export const ModalWrapper = () => {
    const { modalContent, modalOpen, close } = useSlideModalState();
    const wrapper = useRef();

    return (
        <Condition condition={[modalOpen]}>
            <TouchableWithoutFeedback
                onPress={(e) => {
                    if (e.target === wrapper.current) {
                        close();
                    }
                }}
            >
                <View
                    ref={wrapper}
                    style={[
                        {
                            backgroundColor: 'rgba(34,34,34,0.6)',
                            width: '100%',
                            height: '100%',
                            position: 'absolute',
                        },
                    ]}
                >
                    <View
                        style={{
                            position: 'absolute',
                            bottom: 0,
                            left: 0,
                            borderTopLeftRadius: 10,
                            borderTopRightRadius: 10,
                            backgroundColor: '#fff',
                            width: '100%',
                        }}
                    >
                        {modalContent}
                    </View>
                </View>
            </TouchableWithoutFeedback>
        </Condition>
    );
};
