# KM 文档读取方法（km.sankuai.com）

目标：读取内部 KM 文档，例如：[蜂窝诊断任务列表（KM）](https://km.sankuai.com/collabpage/**********)。访问需携带有效登录 Cookie。

> 注意：Cookie 仅用于内部文档读取，可能过期需轮换；请勿提交至公开仓库。

## 1. curl（快速验证）

```bash
curl -sSL 'https://km.sankuai.com/collabpage/**********' \
  -H "Cookie: $(cat memory-bank/km-cookie.md | tr -d '\n')" \
  -o prd/kb/km-**********.html
```

- 也可以直接把 `memory-bank/km-cookie.md` 的内容复制到 `Cookie:` 头。
- 返回 200 为成功；401/302 表示 Cookie 失效或权限不足。

## 2. Node 脚本（推荐）

已有脚本：`scripts/fetch-km-doc.js`

- 运行方式（环境变量传入 Cookie）：
```bash
KM_COOKIE="<your-cookie>" node scripts/fetch-km-doc.js
```
- 或把 Cookie 写入 `memory-bank/km-cookie.md` 后直接运行：
```bash
node scripts/fetch-km-doc.js
```
- 默认输出：`prd/kb/km-**********.html`

可选参数：
- `KM_URL` 自定义目标地址
- `KM_OUT` 自定义输出路径

## 3. 浏览器/Puppeteer

- 已登录浏览器访问链接会自动带上 Cookie。
- Puppeteer 可通过 `page.setExtraHTTPHeaders({ Cookie })` 或持久化会话注入。

## 4. 常见问题

- 401：鉴权失败（Cookie 失效/未登录），更新 Cookie。
- 302 跳登录：需跟随跳转或在已登录环境请求。
- 网络限制：需公司网络或 VPN。

---

引用：[`https://km.sankuai.com/collabpage/**********`](https://km.sankuai.com/collabpage/**********)
