## 产品上下文（Product Context）

### 背景
用户需要在移动端实时查看与管理自己发起的诊断任务，并在任务完成后快速查看结果，减少在多处查找的成本。

### 用户与场景
- **主要用户**: 运营、研发以及相关业务同学。
- **核心场景**:
  - 主界面入口展示当前运行中任务数量（徽标）
  - 抽屉面板展示任务列表，支持状态分类（进行中/成功/失败）
  - 查看任务详情并快速进入任务结果（或自动发起问题查询）

### 目标与价值
- **实时**: 定时轮询 2s，保证状态更新的时效性。
- **直达**: 任务项一键查看结果，或触发与聊天模块的联动查询。
- **体验**: 错误不留白，展示降级内容以保持完整可用。

### 关键流程
1. 用户点击任务列表按钮 → 获取任务状态/列表 → 展示抽屉面板
2. 用户点击任务项 → 检查当前聊天状态 → 可自动发起问题查询或提示等待

### 设计参考
- 原型图：`prd/image.png`
- 需求文档：`prd/任务列表功能PRD.md`


