# 【PRD】小蜜交互改版 v1.2 待办清单

引用来源：
- [KM 文档：小蜜交互改版 v1.2](https://km.sankuai.com/collabpage/2718765799)
- [技术方案](https://km.sankuai.com/collabpage/2722081184)

说明：本清单根据 PRD 与技术方案拆解为工程可执行任务，结合本期范围约束（不做探索Tab、弱网/降级态、埋点与监控、右下悬浮按钮），对齐现有模块。Owner、排期与依赖待各模块负责人补充。

---

## 本期范围与约束
- **仅"对话"页**：不做"探索"Tab
- **问候语**：由接口下发，不做本地兜底
- **分类卡片**：后端下发数据，每卡片4项+前端滑动分页
- **弱网/降级态**：不做
- **输入区**：沿用现有实现
- **右下悬浮按钮**：不做
- **埋点与监控**：不做

## 里程碑与优先级
- P0：头部问候语与Banner、技能分类卡片（4项+滑动分页）、快捷入口、任务列表入口与徽标、"猜你想问"（可选）
- P1：智能体接入扩展点、历史设计稿对齐优化、性能与体验优化
- P2：更多跨端与可访问性增强

---

## P0 任务

### [ ] 1. 头部问候语与Banner（接口下发）
- 需求要点：
  - 问候语由主页接口返回的 `weatherGreeting` 对象渲染，支持天气类型展示
  - Banner 支持内容展示、按钮文字、触发问题等完整配置
- 实现要点：
  - 接口：GET /bee/v2/bdaiassistant/homePage/dialogue
  - 字段结构：
    ```typescript
    weatherGreeting: {
      greeting: string;        // 问候语内容
      weatherTips: string;     // 天气提示
      weatherType: string;     // 天气类型
    }
    banner: {
      bannerContent: string;   // Banner内容
      bannerButton: string;    // 按钮文字
      triggerQuestion: string; // 触发问题
      type: string;           // Banner类型
    }
    ```
  - 空返回时渲染空字符串，不做本地兜底文案
  - Banner 不返回则不占位
- 验收标准：
  - 问候语与天气提示正确展示；Banner 按钮点击可触发对应问题；不同机型布局一致
- Owner：TBD  |  依赖：useHomePageData hook、TypeScript类型定义  |  预估：TBD

### [ ] 2. 技能分类卡片（4项+滑动分页）
- 需求要点：
  - 每张分类卡片固定显示 4 项技能，超出项用横向滑动分页+指示点
  - 支持多层级技能结构（技能 + 子技能列表）
  - 数据完全由后端下发，包含详细的动作配置
- 实现要点：
  - 接口：同主页接口返回 `skillGroups` 数组
  - 数据结构：
    ```typescript
    skillGroups: Array<{
      name: string | null;     // 分组名称
      type: string;            // 分组类型
      skills: Array<{
        abilityType: number;      // 能力类型
        operationType: number;    // 操作类型
        content: string;          // 显示内容
        subAbilityType: null;     // 子能力类型
        url: string;              // 跳转URL
        link: string;             // 链接地址
        openWay: string;          // 打开方式
        isNew: boolean;           // 新功能标识
        subSkillList?: Array<{    // 子技能列表（可选）
          // 结构与技能项相同
          abilityType: number;
          operationType: number;
          content: string;
          // ...其他字段
        }>;
      }>;
    }>
    ```
  - 前端职责：4项/页渲染、滑动分页、指示点同步、点击动作执行、子技能展开逻辑
  - 动作执行：根据 `operationType` 和 `openWay` 确定具体行为（发问、跳转、路由等）
- 验收标准：
  - 分页滑动流畅；指示点状态正确；点击动作按配置执行；子技能展开交互正确
- Owner：TBD  |  依赖：useHomePageData hook、动作映射处理器  |  预估：TBD

### [ ] 3. 快捷入口（后端下发）
- 需求要点：
  - 保留快捷入口区（如驳回查询、点评离分查询、到手价计算、短信查询）
  - 数据与动作由后端下发，与技能项动作协议统一
- 实现要点：
  - 接口：同主页接口返回快捷入口数据
  - 渲染：胶囊按钮样式，支持禁用态（置灰+不可点击）
  - 动作：ask/openUrl/route 三种类型
- 验收标准：
  - 按钮样式一致；禁用态正确；点击动作按配置执行
- Owner：TBD  |  依赖：后端接口、动作协议  |  预估：TBD

### [ ] 4. 任务列表入口与运行中徽标（沿用现有实现）
- 需求要点（结合现有 `TaskList` 方案）：
  - 首页入口展示当前运行中任务数量徽标；点击打开任务列表抽屉
  - 2s 轮询刷新状态；任务项支持"查看结果/继续提问/URL 跳转"等
- 实现要点：
  - 沿用现有 TaskList 组件与接口；调整首页入口位置与样式
  - 抽屉按状态分组（进行中/成功/失败），支持筛选
  - 点击任务项联动聊天：可自动发起问题查询或跳转结果页
- 验收标准：
  - 轮询不卡顿，状态更新及时；任务点击联动正确
- Owner：TBD  |  依赖：现有TaskList组件、聊天联动  |  预估：TBD

### [ ] 5. "猜你想问"（可选，替换"推荐问"）
- 需求要点：
  - 冷启动时替代"推荐问"
  - 用户输入具体内容后，使用线上"猜你想问"逻辑
- 实现要点：
  - 接口：ChatThriftServiceImpl#getRelatedQuestion（封装为代理接口）
  - 冷启动时调用获取 5 条；用户输入后按现有逻辑调用
  - 仅渲染与点击触发，不做埋点
  - 若本期首页不露出该模块，UI 占位为 0
- 验收标准：
  - 冷启动与输入后的逻辑切换正确；点击触发发问正确
- Owner：TBD  |  依赖：现有猜你想问接口  |  预估：TBD

### [ ] 6. 适配与无障碍
- 实现要点：
  - 不同机型适配；触摸反馈、可点击区域 ≥ 44x44；深色模式与分辨率适配
  - 与现有输入区兼容；安全区布局正确
- 验收标准：
  - 关键路径 60fps；无明显掉帧；点击区域达标
- Owner：TBD  |  依赖：设计验收与端测  |  预估：TBD

### [ ] 7. 主页数据Hook（SWR缓存）
- 需求要点：
  - 创建统一的数据获取hook，被多个组件共享使用
  - 使用SWR进行数据缓存，避免重复请求，提升性能
  - 统一处理loading、error、数据刷新等状态
- 实现要点：
  - 创建hook文件：`src/hooks/useHomePageData.ts`
  - 集成SWR进行缓存管理：
    ```typescript
    import useSWR from 'swr';
    import { HomePageDialogueResponse } from '@/types/homePageApi';

    export const useHomePageData = () => {
      const { data, error, isLoading, mutate } = useSWR<HomePageDialogueResponse>(
        '/bee/v2/bdaiassistant/homePage/dialogue',
        fetcher,
        {
          revalidateOnFocus: false,
          revalidateOnReconnect: true,
          dedupingInterval: 30000, // 30秒内去重
        }
      );

      return {
        homePageData: data,
        skillGroups: data?.skillGroups || [],
        weatherGreeting: data?.weatherGreeting,
        banner: data?.banner,
        isLoading,
        error,
        refresh: mutate
      };
    };
    ```
  - 支持手动刷新、错误重试等功能
  - 与现有请求库（useCallerRequest）集成
- 验收标准：
  - 多个组件同时使用时只发起一次请求；缓存机制生效；loading和error状态正确
- Owner：TBD  |  依赖：SWR库、TypeScript类型定义  |  预估：0.5天

### [ ] 8. TypeScript类型定义（基于Schema）
- 需求要点：
  - 根据接口Schema定义完整的TypeScript接口类型
  - 确保类型安全，支持IDE智能提示和编译时检查
- 实现要点：
  - 创建类型文件：`src/types/homePageApi.ts`
  - 定义核心接口：
    ```typescript
    export interface HomePageDialogueResponse {
      skillGroups: SkillGroup[];
      weatherGreeting: WeatherGreeting;
      banner: Banner;
    }

    export interface SkillGroup {
      name: string | null;
      type: string;
      skills: Skill[];
    }

    export interface Skill {
      abilityType: number;
      operationType: number;
      content: string;
      subAbilityType: null;
      url: string;
      link: string;
      openWay: string;
      isNew: boolean;
      subSkillList?: Skill[];
    }

    export interface WeatherGreeting {
      greeting: string;
      weatherTips: string;
      weatherType: string;
    }

    export interface Banner {
      bannerContent: string;
      bannerButton: string;
      triggerQuestion: string;
      type: string;
    }
    ```
- 验收标准：
  - 所有接口字段都有对应的TypeScript类型；编译通过无类型错误；IDE提示准确
- Owner：TBD  |  依赖：最终接口Schema确认  |  预估：0.5天

### [ ] 9. 动作映射与执行逻辑
- 需求要点：
  - 根据技能项的 `abilityType`、`operationType`、`openWay` 字段确定具体执行动作
  - 统一处理发问、跳转、路由等不同类型的交互
- 实现要点：
  - 创建动作处理器：`src/utils/skillActionHandler.ts`
  - 动作类型映射：
    ```typescript
    export enum SkillActionType {
      ASK_QUESTION = 'ask',      // 发起提问
      OPEN_URL = 'openUrl',      // 打开链接
      NAVIGATE = 'navigate',     // 路由跳转
      EXPAND_SUB = 'expandSub'   // 展开子技能
    }

    export interface SkillActionHandler {
      handleSkillClick(skill: Skill): void;
      determineActionType(skill: Skill): SkillActionType;
      executeAction(actionType: SkillActionType, skill: Skill): void;
    }
    ```
  - 支持子技能列表的展开/收起交互
  - 与现有聊天系统的发问逻辑集成
- 验收标准：
  - 不同类型技能点击后执行正确动作；子技能展开交互流畅；与聊天联动正常
- Owner：TBD  |  依赖：动作类型与字段的映射规则确认  |  预估：1天

### [x] 10. 公告支持Markdown渲染 ✅
- 需求要点：
  - 公告内容支持markdown格式，包括粗体、斜体、代码、链接、标题、列表等常用语法
  - 向后兼容纯文本公告，智能检测内容格式
  - 保持现有展开/收起功能，适配深色背景样式
- 实现要点：
  - 基于现有`MemoizedMarkdownInner`组件，创建公告专用markdown样式
  - 智能检测markdown语法，自动选择渲染方式
  - 适配深色背景的颜色方案（白色文字、橙色链接）
  - 保持现有交互功能（展开/收起、关闭）
- 技术实现：
  - 新增文件：`src/components/Chat/ChatFooter/Announcement/announcementMarkdownStyle.ts`
  - 修改文件：`src/components/Chat/ChatFooter/Announcement/index.tsx`
- 验收标准：
  - 支持常用markdown语法（粗体、斜体、代码、链接、标题、列表等）
  - 纯文本公告正常显示，无功能退化
  - 展开/收起功能在markdown内容下正常工作
  - 链接点击正确跳转，样式在深色背景下清晰可见
- Owner：已完成  |  依赖：现有markdown组件  |  预估：已完成

---

## P1 任务
- [ ] 智能体接入扩展点（招商助手、阅数助手等）：
  - 预留入口与路由协议；占位文案与禁用态
- [ ] 历史设计稿对齐与细节还原：
  - 参考历史设计稿链接进行样式微调与一致性修复
- [ ] 性能与体验优化：
  - 首屏渲染、懒加载策略、资源压缩与缓存

---

## 接口与依赖
- 主要接口：
  - 主页数据：`GET /bee/v2/bdaiassistant/homePage/dialogue`
    - 返回字段：`skillGroups`、`weatherGreeting`、`banner`
    - 完整类型定义见 `src/types/homePageApi.ts`
  - 用户配置：`GET userAssistantConfig/get`, `POST userAssistantConfig/finishGuide`
  - 猜你想问：`ChatThriftServiceImpl#getRelatedQuestion`（代理封装）
- 核心数据结构：
  ```typescript
  // 主页接口返回结构
  {
    skillGroups: SkillGroup[];     // 技能分组数据
    weatherGreeting: {             // 天气问候语
      greeting: string;
      weatherTips: string;
      weatherType: string;
    };
    banner: {                      // Banner配置
      bannerContent: string;
      bannerButton: string;
      triggerQuestion: string;
      type: string;
    };
  }
  ```
- 配置依赖：
  - 后端配置：技能分组配置表（包含 abilityType、operationType 映射规则）
  - Lion配置：天气问候语配置、Banner展示规则
- 新增文件：
  - `src/types/homePageApi.ts` - TypeScript类型定义
  - `src/hooks/useHomePageData.ts` - 主页数据获取hook（SWR缓存）
  - `src/utils/skillActionHandler.ts` - 技能动作处理器
- 文档参考：
  - [小蜜交互改版 v1.2](https://km.sankuai.com/collabpage/2718765799)
  - [技术方案](https://km.sankuai.com/collabpage/2722081184)

---

## 风险与前置
- **接口字段变更**：需后端提供明确的Schema文档，确认所有字段的具体含义和取值范围
- **动作映射规则**：需要明确 `abilityType`、`operationType`、`openWay` 字段与具体动作的映射关系
- **子技能交互逻辑**：`subSkillList` 的展开/收起交互方式需要产品明确设计方案
- **SWR依赖管理**：项目需集成SWR库，确保与现有请求机制兼容，避免缓存冲突
- **技能分类配置**：依赖后端配置表数据结构稳定，需要提供测试数据
- **现有组件兼容**：TaskList 组件与输入区需保持现有功能不受影响
- **类型安全**：新增的复杂数据结构需要完善的TypeScript类型定义和运行时校验

---

## 验收清单（抽样）
- [ ] **数据获取Hook**：useHomePageData hook正常工作；SWR缓存生效；多组件共享数据无重复请求
- [ ] **接口数据**：skillGroups、weatherGreeting、banner 字段正确解析；TypeScript类型检查通过
- [ ] **问候语区域**：greeting、weatherTips、weatherType 正确展示；天气类型图标匹配
- [ ] **Banner功能**：bannerContent 展示正确；bannerButton 点击触发 triggerQuestion；type 字段生效
- [ ] **技能分类卡片**：4项/页渲染正确；滑动分页流畅；指示点状态同步
- [ ] **技能点击动作**：根据 abilityType/operationType 执行正确动作；url/link 跳转正确；openWay 生效
- [ ] **子技能列表**：subSkillList 存在时展开/收起交互正常；子技能点击动作正确
- [ ] **新功能标识**：isNew=true 时显示"新"标识；样式符合设计规范
- [ ] **任务模块**：2s 轮询稳定；任务点击联动聊天或跳转结果正确；徽标数量准确
- [ ] **猜你想问**：冷启动5条与输入后逻辑切换正确（如首页露出该模块）
- [ ] **适配兼容**：不同机型布局一致；点击区域≥44x44；与现有输入区兼容；类型安全无报错


