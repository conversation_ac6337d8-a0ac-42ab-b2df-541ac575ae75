/**
 * 公共分类
 * @namespace AutoGeneratedApisTypes
 * 该文件由 @mfe/cc-api-caller 自动生成
 * powered by @mtfe/yapi2service
 */

export type AutoGeneratedApisTypes = {
    /** 统一的灰度逻辑 */
    '/bee/v1/bdaiassistant/common/gray': {
        request: Record<string, never>;
        response: {
            /** 是否走新版本的交互。true: 新版本交互，false：旧版本交互 */
            interactionGray: boolean;
        };
    };
    /**  获取聊天记录 */
    '/bee/v1/bdaiassistant/getChatHistory': {
        request: {
            /** 问题记录id */
            minMsgId?: string;
        };
        response: {
            minMsgId: string;
            msgItems: {
                /** 回答状态(1回答完成) */
                status: number;
                msgId: string;
                subAbilityType?: number;
                /** 能力类型 */
                abilityType: number;
                /** 反馈类型 */
                feedbackType: number;
                /** 类型（1问 2答） */
                type: number;
                /** 消息类型(1.普通纯文本，2.选项型) */
                msgType: number;
                /** 当前纯文本消息(消息类型=普通纯文本才有) 如果消息是问题，那么这个字段是问题的文本 */
                currentContent?: string;
                /** 前序所有消息内容(消息类型=普通纯文本才有) */
                previousContent?: string;
                /** 前置内容(消息类型=选项型才有) */
                prefixTextContent?: string;
                /** 后置内容(消息类型=选项型才有) */
                postTextContent?: string;
                /** 选项，是回答且msgType为选项型才会有 */
                selectionItems?: {
                    /** 能力类型 */
                    abilityType?: number;
                    /** 子能力类型(缺省) */
                    subAbilityType?: number;
                    /** 选项名称 */
                    content: string;
                    /** 用户点击选项之后的操作类型：1.url跳转，2.继续提问 */
                    operationType: number;
                    url?: string;
                }[];
                /** 图片链接(回答类型才可能有) */
                imageList?: string[];
            }[];
        };
    };
    /** 主动关闭会话接口 */
    '/bee/v1/bdaiassistant/closeSession': {
        request: Record<string, never>;
        response: Record<string, never>;
    };
    /** 关联问提查询接口 */
    '/bee/v1/bdaiassistant/getRelatedQuestion': {
        request: {
            /** 关键字(两个字符及以上才能获取关联问) */
            input: string;
            /** 用户业务id */
            bizId: string;
        };
        response: {
            /** 关联问题 */
            questions: string[];
        };
    };
    /** 分页获取当前用户所负责的全量商家 */
    '/bee/v1/bdaiassistant/common/getOwnPoiListByPage': {
        request: {
            pageNum: string;
            pageSize: string;
            data: string;
            /** 商家诊断: poiDiagnose  */
            type: string;
            /** 过滤条件，如果有多个条件，通过逗号分隔。canPoiDiagnoseList：表明是否支持商家诊断 */
            filter: string;
        };
        response: {
            poiList: {
                /** 商家ID */
                id: number;
                /** 商家名称 */
                name: string;
                /** 商家图片链接 */
                url: string;
                extra: {
                    /** 表示是否支持商家诊断 */
                    supportDiagnostic: boolean;
                };
            }[];
        };
    };
    /** 创建会话 */
    '/bee/v1/bdaiassistant/openSession': {
        request: {
            /** 来源 */
            source: string;
            /** 额外信息，json 格式，针对 source = xianfu，需要传入 href 信息 */
            extra?: string;
            /** 版本号，用于区分灰度，目前V3 */
            version: string;
        };
        response: {
            /** 是否有聊天记录标识 */
            existChatRecord: boolean;
            /** (提问引导)还有下组引导标识 */
            hasNext: boolean;
            /** (提问引导)分页 */
            pageNum: number;
            /** (提问引导)选项前内容 */
            prefixTextContent: string;
            /** (提问引导)选项后内容 */
            postTextContent: string;
            /** (提问引导)具体选项 */
            selectionItems: {
                /** 能力类型 */
                abilityType?: number;
                /** 子能力类型(缺省) */
                subAbilityType?: number;
                /** 选项名称 */
                content: string;
                /** 用户点击选项之后的操作类型：1.url跳转，2.继续提问 */
                operationType: number;
                /** 跳转链接 */
                url?: string;
                field_1: string;
                field_2: string;
                field_3: string;
                field_4: string;
            }[];
            /** 当前的会话ID */
            sessionId: number;
        };
    };
    /** 刷新会话 */
    '/bee/v1/bdaiassistant/refreshSession': {
        request: {
            /** 来源 */
            source: string;
            /** 额外信息，json 格式 */
            extra: string;
        };
        response: {
            /** 是否展示刷新提示语 */
            showRefreshMsg: boolean;
            /** 会话Id */
            sessionId: number;
        };
    };
    /** 刷新目录 */
    '/bee/v1/bdaiassistant/fetchCategoryItems': {
        request: {
            /** 后端传入的字段 currentContent.options.tabs.label */
            name?: string;
            /** 后端传入的字段	pageNum */
            pageNum?: string;
            entryPoint: string;
        };
        response: {
            status?: string;
            abilityType?: string;
            msgId?: string;
            questionMsgId?: string;
            /** 是否还有数据 */
            hasNext?: string;
            /** 下一页 */
            pageNum?: number;
            selectionItems?: {
                content: string;
                operationType: number;
                abilityType: number;
                subAbilityType: number;
                url: string;
            }[];
        };
    };
    /** 反馈接口 */
    '/bee/v1/bdaiassistant/feedBackForChat': {
        request: {
            /** chatId */
            chatRecordId: string;
            /** 反馈类型 */
            type: number;
            /** 反馈内容 */
            feedBackContent?: string;
        };
        response: Record<string, never>;
    };
    /** 工具栏工具顺序调整 */
    '/bee/v1/bdaiassistant/common/toolbar/order': {
        request: {
            /** 工具列表 */
            toolList: string[];
        };
        response: Record<string, never>;
    };
    /** 打点 */
    '/bee/v1/bdaiassistant/trace/log': {
        request: {
            /** 入口点位置。toolbar：工具栏； */
            entryPoint?: string;
            /** 事件类型，click:点击 */
            eventType?: string;
            /** 针对工具栏的点击事件，填充 /bee/v1/bdaiassistant/common/getToolbarConfig 接口返回的完整信息 */
            content: string;
        };
        response: Record<string, never>;
    };
    /** 提问 */
    '/bee/v1/bdaiassistant/submitQuery': {
        request: {
            /** 能力类型 */
            abilityType: number;
            /** 子能力类型(文本输入可以不填) */
            subAbilityType?: number;
            /** 问题文本内容 */
            content: string;
            /** 业务线ID */
            bizId: string;
            /** 问题来源。Input：手工输入；Voice：语音输入；AssociativeInput：联想输入；Options:{msgId} ：选项列表，其中msgId是当前消息的 msgId； */
            referer: string;
        };
        response: {
            /** (提问)消息ID */
            questionMsgId: string;
            /** 能力类型 */
            abilityType: number;
            /** 子能力类型(文本输入可以不填) */
            subAbilityType?: number;
            /** 响应消息ID */
            msgId?: string;
        };
    };
    /** 签署使用说明 */
    '/bee/v1/bdaiassistant/signInstructions': {
        request: Record<string, never>;
        response: Record<string, never>;
    };
    /** 【开放平台】会话 */
    '/bee/v1/bdaiassistant/openapi/chat': {
        request: Record<string, never>;
        response: Record<string, never>;
    };
    /** 获取BD的技能树 */
    '/bee/v1/bdaiassistant/getWmBdAbilityType': {
        request: Record<string, never>;
        response: {
            abilityTypes: {
                id: any;
                /** 展示文本 */
                text: string;
                /** 是否展示 */
                show: string;
                /** 描述 */
                description: string;
            }[];
        };
    };
    /** 获取ToolBar信息 */
    '/bee/v1/bdaiassistant/common/getToolbarConfig': {
        request: Record<string, never>;
        response: {
            options?: {
                /** 枚举值，后端返回，透传回后端 */
                abilityType: number;
                /** 暂时没有值 */
                subAbilityType: number;
                /** 枚举值，必填，1：跳转链接，2：发送消息 */
                operationType: number;
                /** 展示内容，必填 */
                content: string;
                /** 可选，operationType为1时的跳链 */
                url: string;
                /** 是否置顶 */
                top: boolean;
                /** 图标链接 */
                link: string;
            }[];
        };
    };
    /** 获取下一组回答(换一换) */
    '/bee/v1/bdaiassistant/fetchNextGroupAnswer': {
        request: {
            /** 能力类型 */
            abilityType: number;
            /** 子能力类型 */
            subAbilityType?: number;
            /** 回答消息ID */
            msgId: string;
            /** 页码(请求下一组选项时必填) */
            pageNum?: number;
        };
        response: {
            /** 回答状态(1回答完成,0未完成) */
            status: string;
            /** 提问消息ID */
            questionMsgId: string;
            /** 回答消息ID */
            msgId: string;
            /** 能力类型 */
            abilityType: string;
            /** 是否有其他回答 */
            hasNext?: string;
            /** 页码 */
            pageNum?: number;
            selectionItems?: {
                /** 能力类型 */
                abilityType?: number;
                /** 子能力类型(缺省) */
                subAbilityType?: number;
                /** 选项名称 */
                content: string;
                /** 用户点击选项之后的操作类型：1.url跳转，2.继续提问 */
                operationType: number;
                url?: string;
            }[];
        };
    };
    /** 获取使用说明内容 */
    '/bee/v1/bdaiassistant/getInstructions': {
        request: Record<string, never>;
        response: {
            /** 使用说明文本 */
            instructions: string;
        };
    };
    /** 获取回答 */
    '/bee/v1/bdaiassistant/fetchAnswer': {
        request: {
            /** 能力类型 */
            abilityType: number;
            /** 子能力类型 */
            subAbilityType?: number;
            /** (问题)记录id */
            questionMsgId: string;
            /** 回答消息ID */
            msgId?: string;
            /** 页码(请求下一组选项时必填) */
            pageNum?: string;
            /** 接口版本。取值有V1、V2，不传时，默认为V1 */
            version?: string;
        };
        response: {
            /** 回答状态(1回答完成,0未完成) */
            status: number;
            /** 是否涉敏 true涉敏，false不涉敏。为true时，currentContent为提示信息 */
            sensitive?: boolean;
            /** 问题消息ID */
            questionMsgId?: string;
            /** 回答消息ID */
            msgId: string;
            /** 能力类型 */
            abilityType: number;
            subAbilityType?: number;
            /** 是否有其他回答 */
            hasNext?: boolean;
            /** 页码 */
            pageNum?: number;
            /** 类型  1 QUESTION 用户问题  2 ANSWER 回答 */
            type: number;
            /** 反馈类型（1点赞 2点踩） */
            feedbackType?: number;
            /** 服务期时间 */
            respTime?: number;
            /** 消息类型(1.普通纯文本，2.选项型) */
            msgType?: number;
            /** 当前纯文本消息(消息类型=普通纯文本才有) */
            currentContent?: string;
            /** 前序所有消息内容(消息类型=普通纯文本才有) */
            previousContent?: string;
            /** 前置内容(消息类型=选项型才有) */
            prefixTextContent?: string;
            /** 后置内容(消息类型=选项型才有) */
            postTextContent?: string;
            imageList?: string[];
            selectionItems?: {
                /** 能力类型 */
                abilityType?: number;
                /** 子能力类型(缺省) */
                subAbilityType?: number;
                /** 选项名称 */
                content: string;
                /** 用户点击选项之后的操作类型：1.url跳转，2.继续提问 */
                operationType: number;
                /** 跳转链接 */
                url?: string;
            }[];
            /** 标签列表，poiId：表明需要获取商家ID信息； */
            tags: string[];
        };
    };
    /** 获取天气 */
    '/bee/v1/bdaiassistant/common/weather': {
        request: {
            /** 纬度 */
            lat: string;
            /** 经度 */
            lng: string;
        };
        response: {
            /** 问候语 */
            greeting: string;
            /** 天气描述 */
            weatherTips: string;
            /** 天气类型，rain：雨，hot：高温，cold：降温，default: 兜底 */
            weatherType: string;
        };
    };
    /** 获取工具栏的问题列表 */
    '/bee/v1/bdassistant/toolbar/question/list': {
        request: Record<string, never>;
        response: string[];
    };
    /** 获取当前用户所负责的全量商家 */
    '/bee/v1/bdaiassistant/common/allOwnPoiList': {
        request: Record<string, never>;
        response: {
            poiList: {
                /** 商家ID */
                id: number;
                /** 商家名称 */
                name: string;
                /** 商家图片链接 */
                url: string;
            }[];
        };
    };
    /** 获取是否签署过使用说明 */
    '/bee/v1/bdaiassistant/existSignRecord': {
        request: Record<string, never>;
        response: {
            /** 是否签署过使用说明 true:签署过 */
            sign: boolean;
        };
    };
    /** 获取灰度开关接口 */
    '/bee/v1/bdaiassistant/getGraySwitch': {
        request: {
            /** BD业务id */
            bizId: string;
        };
        response: {
            /** 灰度结果 true:命中灰度，展示机器人 */
            gray: boolean;
        };
    };
    /** 获取热门问题 */
    '/bee/v1/bdaiassistant/question/hot': {
        request: Record<string, never>;
        response: {
            hotQuestion: {
                /** 能力类型 */
                abilityType: number;
                /** 子能力类型(缺省) */
                subAbilityType: number;
                /** 名称 */
                content: string;
                /** 用户点击选项之后的操作类型：1.url跳转，2.继续提问 */
                operationType: string;
                /** 跳转链接 */
                url: string;
                /** 图标链接 */
                link: string;
                /** 是否高亮 */
                isNew: boolean;
            }[];
        };
    };
    /** 获取粘贴板内容 */
    '/bee/v2/bdaiassistant/clipboard/content': {
        request: {
            /** 粘贴板内容类型。poiDetailRejectTask：商家驳回的粘贴板内容 */
            type: string;
            /** 会话ID */
            sessionId: string;
        };
        response: {
            /** 粘贴板内容 */
            content: string;
            /** 提示内容 */
            toast: string;
            /** 什么时候触发 toast。clickTT： 当点击 TT 时触发。 */
            when: string;
        };
    };
    /** 触发场景 */
    '/bee/v1/bdaiassistant/triggerScene': {
        request: {
            /** 场景ID，直接从 url 中解析 */
            id: number;
            /** 传参，json 格式。直接从 url 中解析 */
            params: string;
        };
        response: {
            /** ID */
            id: string;
            /** 前置内容 */
            prefixTextContent: string;
            /** 当前内容 */
            currentContent: string;
            /** 后置内容 */
            postTextContent: string;
            /** 类型，1：问题，2：回答 */
            type: number;
            /** 消息类型(1.普通纯文本，2.选项型) */
            msgType: number;
            selectionItems: {
                /** 选项名称 */
                content: string;
                /** 用户点击选项之后的操作类型：1.url跳转，2.继续提问 */
                operationType: number;
                /** 跳转链接 */
                url: string;
            }[];
        }[];
    };
    /** 获取任务列表 */
    '/bee/v2/bdaiassistant/job/poiDiagnosis/runningToday': {
        request: {};
        response: {
            /** 正在运行中的任务个数 */
            runnings: number;
            /** 是否需要点击 */
            needToClick: boolean;
            /** 是否展示完成 */
            showCompleted: boolean;
        };
    };
    /** 获取任务详情 */
    '/bee/v2/bdaiassistant/job/poiDiagnosis/listToday': {
        request: {};
        response: {
            jobList: {
                /** 创建时间 */
                createTime: string;
                itemList: {
                    /** 任务类型 */
                    type: string;
                    /** 商家ID */
                    poiId: number;
                    /** 商家名称 */
                    poiName: string;
                    /** 商家头像 */
                    poiAvator: string;
                    /** 任务状态 success: 成功；fail : 失败；init: 运行中；*/
                    status: string;
                    /** 能力类型 */
                    abilityType: number;
                    /** 操作类型 */
                    operationType: number;
                    /** 内容 */
                    content: string;
                    /** 任务项id */
                    id: number;
                    /** 是否展示红点 */
                    showRedDot: boolean;
                }[];
            }[];
        };
        /** 总数 */
        total: number;
        /** 成功个数 */
        success: number;
        /** 失败个数 */
        fail: number;
    };
    /** 清除任务项红点 */
    '/bee/v2/bdaiassistant/job/clearRedDot': {
        request: {
            /** 任务项id */
            id: number;
        };
    };
};
