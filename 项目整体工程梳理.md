项目整体工程梳理：

### 项目概述
这是一个基于 MRN (美团 React Native) 框架开发的移动应用，名为"蜜蜂助手"(bee-assistant)，使用 TypeScript 和 React Native 技术栈。

### 技术栈
- **框架**：MRN (美团 React Native)
- **语言**：TypeScript
- **状态管理**：Zustand
- **UI组件**：@roo/roo-rn、@mfe/bee-foundation 系列组件
- **API调用**：@mfe/cc-api-caller-bee
- **导航**：@mfe/bee-foundation-navigation
- **不可变数据**：immer

### 项目结构
```
├── src/                      # 源代码目录
│   ├── api/                  # API 接口定义
│   ├── assets/               # 静态资源
│   ├── components/           # 可复用组件
│   ├── consts/               # 常量定义
│   ├── hooks/                # 自定义 Hooks
│   │   ├── biz/              # 业务相关 Hooks
│   │   └── mock/             # 模拟数据相关 Hooks
│   ├── pages/                # 页面组件
│   │   ├── chat/             # 聊天相关页面
│   │   ├── complain/         # 投诉相关页面
│   │   ├── draggedFlatListDemo/ # 拖拽列表示例
│   │   └── horizontalTable/  # 水平表格页面
│   ├── store/                # 状态管理
│   ├── types/                # 类型定义
│   ├── utils/                # 工具函数
│   ├── TWS/                  # 样式工具
│   ├── App.tsx               # 应用入口组件
│   └── routers.tsx           # 路由配置
├── bee-assistant-main.tsx    # 主入口文件
├── mrn.config.js             # MRN 配置
├── package.json              # 项目依赖
└── tsconfig.json             # TypeScript 配置
```

### 核心功能模块
1. **聊天功能**：应用的主要功能，包含消息发送、接收和展示
2. **投诉处理**：处理用户投诉的功能
3. **水平表格**：展示数据的特殊表格组件
4. **拖拽列表**：可拖拽排序的列表组件

### 状态管理
使用 Zustand 进行状态管理，主要状态包括：
- **message.ts**：消息相关状态
- **file.ts**：文件相关状态
- **uiState.ts**：UI 相关状态
- **task.ts**：任务相关状态
- **user.ts**：用户相关状态

### 路由系统
使用 @mfe/bee-foundation-navigation 和 @react-navigation/native-stack 实现路由导航，主要路由包括：
- Chat：聊天页面（默认首页）
- Instruction：指令页面
- Complain：投诉页面
- HorizontalTable：水平表格页面
- DraggedFlatListDemo：拖拽列表示例页面

### 组件系统
项目包含丰富的组件，如：
- Chat 相关组件：ChatContent、ChatFooter、ChatHeader 等
- 基础组件：Buttons、Table、Space 等
- 功能组件：VoiceInput、ImageGallery、MessageBox 等

### 开发流程
1. **环境设置**：
   - 使用 Node.js 18 或 20 版本
   - 设置 `NODE_OPTIONS="--openssl-legacy-provider --max-old-space-size=4096"`

2. **启动项目**：
   ```bash
   yarn install
   yarn startMain
   ```

3. **新功能开发**：
   - 在 pages 目录创建新页面
   - 在 routers.tsx 中添加路由配置
   - 在 components 目录添加需要的组件
   - 在 store 目录添加状态管理逻辑

4. **代码风格**：
   - 使用函数组件和 Hooks
   - 使用 TypeScript 类型定义
   - 遵循项目已有的代码组织方式和模式

### 项目特点
1. 使用 MRN 框架，适配美团内部生态
2. 完善的 TypeScript 类型系统
3. 组件化设计，高度可复用
4. 使用 Zustand 进行状态管理，结合 immer 实现不可变数据更新
5. 丰富的自定义 Hooks 封装业务逻辑