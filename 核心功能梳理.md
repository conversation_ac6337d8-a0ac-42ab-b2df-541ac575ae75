### 蜜蜂助手核心功能实现详解

#### 1. 聊天功能实现

聊天功能是应用的核心，主要由以下几个部分组成：

##### 1.1 消息状态管理 (src/store/message.ts)

使用 Zustand 结合 Immer 实现不可变状态更新：

```typescript
// 创建消息状态管理
export const createMessage = (uiState: UiStateAndActions) =>
    create<State>((set, get) => ({
        // 消息列表
        messageList: [],
        // 历史消息列表
        historyMessageList: [],
        // 会话ID
        sessionId: [],
        // 是否正在轮询消息
        isPollingMessage: false,
        
        // 发送消息
        send: (message, bizId, entryPointType, entryPoint) => {
            if (!message) return;
            if (typeof message === 'string') {
                get().sendMessage({
                    content: message,
                    abilityType: AbilityType.GENERAL,
                    bizId,
                    entryPointType,
                    entryPoint,
                });
                return;
            }
            get().sendMessage({
                entryPointType,
                bizId,
                entryPoint,
                ...message,
            });
        },
        
        // 添加消息
        add: (m, mode = AddMode.APPEND) => {
            set(
                produce<State>((state) => {
                    if (mode === AddMode.APPEND) {
                        state.messageList.push(m);
                    } else {
                        state.messageList.unshift(m);
                    }
                }),
            );
        },
        
        // 更新消息状态
        mutateMsg: (msgId, payload) => {
            set(
                produce<State>((state) => {
                    const index = state.messageList.findIndex(
                        (it) => it.msgId === msgId,
                    );
                    if (index === -1) return;
                    
                    let finalPayload = payload;
                    if (typeof payload === 'function') {
                        const msg = state.messageList[index];
                        finalPayload = payload(msg);
                    }
                    Object.keys(finalPayload).forEach(
                        (key) => (state.messageList[index][key] = finalPayload[key]),
                    );
                }),
            );
        },
    }));
```

##### 1.2 聊天页面结构 (src/pages/chat/ChatNew.tsx)

聊天页面由三个主要部分组成：头部、内容区和底部输入区：

```typescript
const Chat = (props) => {
    // ...
    return (
        <View style={{ flex: 1 }}>
            {/* 背景图 */}
            <RNImage
                source={bgImage}
                style={styles.imageBg}
                resizeMode="cover"
            />
            
            {/* 头部 */}
            <ChatHeader />
            
            {/* 内容区 */}
            <ChatContent
                ref={scrollViewRef}
                inputRef={inputRef}
                onScroll={onScroll}
            />
            
            {/* 底部输入区 */}
            <ChatFooter
                onFocus={onFocus}
                onBlur={onBlur}
                onVoiceLongPress={onVoiceLongPress}
                offset={offset.current}
                voiceInputOpen={voiceInputOpen}
            />
            
            {/* 语音输入组件 */}
            <VoiceInput2
                visible={voiceInputOpen}
                onClose={onVoiceClose}
                onSend={onVoiceSend}
            />
        </View>
    );
};
```

##### 1.3 消息展示 (src/components/MessageBox)

消息展示组件根据消息类型展示不同的内容：

```typescript
const MessageBox = (props: MessageBox) => {
    switch (props.data.type) {
        case MessageType.QUESTION:
            return <Question {...props} />;
        case MessageType.ANSWER:
            return <Answer {...props} />;
        case MessageType.SYSTEM:
            return <SystemMessage {...props} />;
    }
    return null;
};
```

##### 1.4 回答内容组件 (src/components/MessageBox/Answer)

回答内容组件根据消息状态和类型展示不同的内容：

```typescript
const AnswerContent = (props: AnswerContent) => {
    // 处理取消会话状态
    if ([MessageStatus.STOPPED, MessageStatus.DONE_AFTER_STOP].includes(props.data.status) && isEmptyAnswer(props.data)) {
        return <Text>已取消会话</Text>;
    }
    
    // 处理加载状态
    if (props.data.status === MessageStatus.TO_GENERATE || isEmptyAnswer(props.data)) {
        return <MessageLoading />;
    }
    
    // 处理错误状态
    if (props.data.status === MessageStatus.ERROR || props.data.sensitive) {
        return (
            <TextMessage
                data={props.data}
                key={props.data.msgId}
                shouldTyping={false}
                onTypedChange={props.onTypedChange}
            />
        );
    }
    
    // 根据消息类型展示不同内容
    switch (props.data.msgType) {
        case MessageContentType.WELCOME:
            return <WelcomeSelectionMessage data={props.data} />;
        case MessageContentType.WITH_OPTIONS:
            return <SelectionMessage data={props.data} />;
        default:
            return (
                <TextMessage
                    data={props.data}
                    key={props.data.msgId}
                    onTypedChange={props.onTypedChange}
                />
            );
    }
};
```

##### 1.5 打字机效果实现 (src/components/MessageBox/Answer/AnswerContent/TextMessage.tsx)

使用 useInterval 实现打字机效果：

```typescript
const TextMessage = (props: TextMessage) => {
    const message = props.data;
    const shouldTyping = props.shouldTyping ?? true;
    
    // 将消息内容转换为字符数组
    const [originData, setOriginData] = useState([]);
    const { currentContent } = message || {};
    
    useEffect(() => {
        let draftOriginData = delta2message(currentContent);
        draftOriginData = draftOriginData
            .map((v) => {
                if (isTextType(v.type)) {
                    return v.insert
                        .split('')
                        .map((str) => ({ ...v, insert: str }));
                }
                return [v];
            })
            .reduce((pre, cur) => [...pre, ...cur], []);
        setOriginData(draftOriginData);
        
        // 如果不需要打字动画则直接展示全部内容
        if (props.data.history || !shouldTyping || status === MessageStatus.STOPPED || status === MessageStatus.DONE) {
            setTypedIndex(draftOriginData.length);
        }
    }, [currentContent]);
    
    // 打字机效果实现
    const [typedIndex, setTypedIndex] = useState(0);
    const clear = useInterval(
        () => {
            if (typedIndex <= originData.length) {
                requestAnimationFrame(() => {
                    setTypedIndex(typedIndex + 1);
                    delayScrollToEnd(200);
                });
            }
        },
        shouldTyping ? getTypeWriterSpeed(status, originData.length - typedIndex) : null,
    );
    
    // 根据打字进度展示内容
    const typed = useMemo(() => {
        return originData.slice(0, typedIndex);
    }, [originData, typedIndex]);
    
    // 渲染 Markdown 内容
    return <MemoizedMarkdown messages={typed} />;
};
```

#### 2. 输入交互功能

##### 2.1 输入框组件 (src/components/Chat/ChatFooter/InputPlugin/InputPluginNew.tsx)

输入框组件包含文本输入、语音输入切换和工具栏：

```typescript
const InputPlugin = (props: ChatFooter) => {
    const [isVoice, { toggle }] = useBoolean();
    const { send } = useMessage((state) => state.input);
    const { text } = useMessage((state) => state.input);
    
    // 发送消息
    const onSend = () => {
        if (!text || isPollingMessage) return;
        send();
        trackButtonClick('chat_send');
    };
    
    return (
        <View style={styles.container}>
            {/* 语音/键盘切换按钮 */}
            <TouchableOpacity onPress={toggle}>
                <RNImage
                    source={isVoice ? keyboardImg : NetImages.voice}
                    style={{ width: IconSize, height: IconSize }}
                />
            </TouchableOpacity>
            
            {/* 文本输入框或语音按钮 */}
            {isVoice ? (
                <TouchableOpacity
                    onLongPress={onVoiceLongPress}
                    style={styles.voiceButton}
                >
                    <Text style={styles.voiceText}>按住说话</Text>
                </TouchableOpacity>
            ) : (
                <AiTextInput
                    onFocus={props.onFocus}
                    onBlur={props.onBlur}
                    onSend={onSend}
                />
            )}
            
            {/* 工具栏按钮 */}
            <TouchableOpacity onPress={toggleToolBarModal}>
                <RNImage
                    source={NetImages.plus}
                    style={{ width: IconSize, height: IconSize }}
                />
            </TouchableOpacity>
            
            {/* 工具栏弹窗 */}
            <Modal
                visible={toolBarModalVisible}
                transparent
                animationType="none"
                onRequestClose={closeModal}
            >
                {/* 工具栏内容 */}
            </Modal>
        </View>
    );
};
```

##### 2.2 消息发送 (src/hooks/useSendMessage.ts)

封装消息发送逻辑，并添加埋点：

```typescript
export const useSendMessage = () => {
    const { bizId } = useBizInfo();
    const sendMsg = useMessage((state) => state.send);
    const setShowHome = useUiState((state) => state.setShowHome);

    const send = (
        p: Parameters<typeof sendMsg>[0],
        entryPointType: EntryPointType,
        entryPoint?: string,
    ) => {
        setShowHome(false);
        sendMsg(p, bizId, entryPointType, entryPoint);
        trackEvent(
            'chat_submit_question',
            {
                category_type: entryPoint || entryPointType,
                msg_content: p,
            },
            TrackEventType.MC,
        );
    };

    return { send };
};
```

#### 3. 状态管理与数据流

##### 3.1 消息类型定义 (src/types/index.ts)

定义了消息的类型和状态：

```typescript
export const enum MessageType {
    SYSTEM = -1,
    QUESTION = 1,
    ANSWER,
}

export const enum MessageContentType {
    WELCOME = -1,
    TEXT = 1,
    WITH_OPTIONS,
}

export const enum MessageStatus {
    STOPPED = -11,      // 用户主动停止
    ERROR = -10,        // 消息轮询失败
    DONE_AFTER_STOP = -3, // 用户点击停止后的状态
    TYPING = -2,        // 正在动画输出中
    TO_GENERATE = -1,   // 用户刚发完消息，后端还没返回前的状态
    GENERATING = 0,
    DONE,
}

export type Message = Omit<
    APISpec['/bee/v1/bdaiassistant/fetchAnswer']['response'],
    'previousContent'
> & {
    history?: boolean;
    lastStatus?: MessageStatus;
    req?: MessageSendReq;
    entryPointType?: EntryPointType;
    isQuestion?: boolean;
    previousContent?: any[];
};
```

##### 3.2 消息轮询机制

消息发送后，通过轮询获取回答内容：

```typescript
// 发送消息
sendMessage: async (req) => {
    // 添加问题消息
    const questionMsgId = `${Math.random()}`;
    get().add({
        msgId: questionMsgId,
        type: MessageType.QUESTION,
        currentContent: req.content,
        status: MessageStatus.DONE,
    });
    
    // 添加回答消息（初始状态为待生成）
    const msgId = `${Math.random()}`;
    get().add({
        msgId,
        questionMsgId,
        type: MessageType.ANSWER,
        status: MessageStatus.TO_GENERATE,
        currentContent: '',
    });
    
    // 发送请求
    const res = await apiCaller.post('/bee/v1/bdaiassistant/submitQuery', {
        ...req,
        version: 'V3',
        sessionId: get().getLatestSessionId(),
    });
    
    // 开始轮询获取回答
    if (res.code === 0) {
        get().startPolling(msgId, res.data.msgId);
    } else {
        get().mutateMsg(msgId, { status: MessageStatus.ERROR });
    }
},

// 开始轮询
startPolling: (localMsgId, remoteMsgId) => {
    const poll = async () => {
        if (!get().isPollingMessage) return;
        
        try {
            const res = await apiCaller.post('/bee/v1/bdaiassistant/fetchAnswer', {
                msgId: remoteMsgId,
            });
            
            if (res.code === 0) {
                // 更新消息内容
                get().mutateMsg(localMsgId, {
                    ...res.data,
                    status: res.data.status,
                    lastStatus: res.data.status,
                });
                
                // 如果消息生成完成，停止轮询
                if (res.data.status === MessageStatus.DONE) {
                    get().setIsPollingMessage(false);
                    return;
                }
                
                // 继续轮询
                setTimeout(poll, 1000);
            } else {
                get().mutateMsg(localMsgId, { status: MessageStatus.ERROR });
                get().setIsPollingMessage(false);
            }
        } catch (e) {
            get().mutateMsg(localMsgId, { status: MessageStatus.ERROR });
            get().setIsPollingMessage(false);
        }
    };
    
    get().setIsPollingMessage(true);
    poll();
}
```

#### 4. 组件复用与样式管理

##### 4.1 条件渲染组件 (src/components/Condition/Condition.tsx)

简化条件渲染的组件：

```typescript
const Condition = ({ condition, children }) => {
    if (!Array.isArray(condition)) {
        return condition ? children : null;
    }
    
    return condition.some(Boolean) ? children : null;
};
```

##### 4.2 样式工具 (src/TWS)

封装了常用的样式工具函数，类似 Tailwind CSS：

```typescript
const TWS = {
    // 弹性布局
    flex: {
        row: { flexDirection: 'row' },
        col: { flexDirection: 'column' },
        center: { justifyContent: 'center', alignItems: 'center' },
        between: { justifyContent: 'space-between' },
        around: { justifyContent: 'space-around' },
    },
    
    // 边距
    margin: {
        t4: { marginTop: 4 },
        t8: { marginTop: 8 },
        t12: { marginTop: 12 },
        // ...更多边距
    },
    
    // 字体
    text: {
        sm: { fontSize: 12 },
        base: { fontSize: 14 },
        lg: { fontSize: 16 },
        // ...更多字体大小
    },
    
    // 颜色
    color: {
        primary: { color: '#1890ff' },
        success: { color: '#52c41a' },
        warning: { color: '#faad14' },
        // ...更多颜色
    },
};
```

### 总结

蜜蜂助手的核心功能实现主要包括：

1. **消息系统**：使用 Zustand 和 Immer 管理消息状态，实现消息的发送、接收和展示
2. **打字机效果**：通过 useInterval 和字符分割实现打字机效果，提升用户体验
3. **组件化设计**：将 UI 拆分为可复用的组件，如 MessageBox、ChatFooter、TextMessage 等
4. **状态管理**：使用 Zustand 进行状态管理，结合 Immer 实现不可变数据更新
5. **API 调用**：使用 apiCaller 进行 API 调用，实现消息的发送和轮询
6. **样式管理**：使用 StyleSheet 和 TWS 工具函数管理样式

这种模块化、组件化的设计使得代码结构清晰，易于维护和扩展。同时，通过状态管理和 API 调用的封装，使得业务逻辑与 UI 展示分离，提高了代码的可测试性和可维护性。